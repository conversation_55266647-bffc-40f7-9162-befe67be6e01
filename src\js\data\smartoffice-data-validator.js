/**
 * @file SmartOffice数据验证器
 * @description 提供数据验证和校验功能，支持CSV数据、透视表配置等验证
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function DataValidator
 * @description 数据验证器构造函数
 * @constructor
 * @param {Object} options - 验证选项
 * @param {boolean} options.strictMode - 严格模式（默认false）
 * @param {number} options.maxRows - 最大行数限制（默认100000）
 * @param {number} options.maxColumns - 最大列数限制（默认1000）
 * @param {Array} options.requiredFields - 必需字段列表
 */
function DataValidator(options) {
    // 默认配置 - 放宽验证条件以支持更大的数据集
    this.options = SmartOffice.Utils.Helpers.extend({
        strictMode: false,        // 严格模式
        maxRows: SmartOffice.Config.MAX_ROWS || 100000,         // 使用全局配置的最大行数
        maxColumns: SmartOffice.Config.MAX_FIELDS || 1000,      // 使用全局配置的最大列数
        requiredFields: [],      // 必需字段
        allowEmptyValues: true,  // 允许空值
        validateDataTypes: false, // 放宽数据类型验证，提高兼容性
        allowMixedTypes: true,   // 允许混合数据类型
        tolerateErrors: true     // 容忍部分错误，不中断处理
    }, options || {});

    // 验证规则
    this.validationRules = {
        // 字段名验证规则 - 放宽字段名限制
        fieldName: {
            minLength: 1,
            maxLength: 500,  // 增加字段名最大长度
            pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5\s\-\.\(\)\[\]\/\\:;,'"!@#$%^&*+=<>?|`~]+$/, // 允许更多特殊字符
            reserved: [] // 移除保留字限制，提高兼容性
        },

        // 数据值验证规则 - 放宽数据值限制
        dataValue: {
            maxLength: 50000,     // 增加单个值最大长度
            allowedTypes: ['string', 'number', 'boolean', 'date', 'object', 'array', 'null', 'undefined'] // 支持更多数据类型
        },

        // 透视表配置验证规则 - 放宽配置限制
        pivotConfig: {
            nameMinLength: 1,
            nameMaxLength: 200,   // 增加配置名称最大长度
            maxFieldsPerType: 50, // 增加每种类型最大字段数
            requiredProperties: ['name'] // 只要求名称为必需属性，其他可选
        }
    };

    // 数据类型检测器 - 简化为三种核心类型：数值、日期、时间
    this.dataTypeDetectors = {
        // 数值类型：整数、小数、百分比、货币等数字格式
        number: /^-?\d+([.,]\d+)?([eE][+-]?\d+)?%?$|^[¥$€£￥＄]?\s?-?\d+([.,]\d+)?$/,

        // 日期类型：各种日期格式
        date: /^\d{1,4}[-\/年.]\d{1,2}[-\/月.]\d{1,2}日?$|^\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4}$/,

        // 时间类型：时间格式（包括带日期的时间）
        time: /^\d{1,2}[:时]\d{1,2}([:分]\d{1,2}秒?)?(\s?(AM|PM|上午|下午))?$|^\d{1,4}[-\/年.]\d{1,2}[-\/月.]\d{1,2}日?\s+\d{1,2}[:时]\d{1,2}([:分]\d{1,2}秒?)?$/
    };

    SmartOffice.log('info', 'DataValidator初始化完成');
}

/**
 * @function DataValidator.prototype.validateParsedData
 * @description 验证解析后的CSV数据
 * @param {Object} parsedData - 解析后的数据对象
 * @returns {Object} 验证结果 {isValid: boolean, message: string, warnings: Array}
 */
DataValidator.prototype.validateParsedData = function(parsedData) {
    const result = {
        isValid: true,
        message: '',
        warnings: [],
        errors: [],
        statistics: {
            totalRows: 0,
            totalColumns: 0,
            emptyValues: 0,
            invalidValues: 0,
            dataTypes: {}
        }
    };

    try {
        // 基本结构验证
        if (!parsedData || typeof parsedData !== 'object') {
            return this.createErrorResult('解析数据格式无效');
        }

        // 验证必需属性
        if (!Array.isArray(parsedData.data)) {
            return this.createErrorResult('数据字段缺失或格式无效');
        }

        if (!Array.isArray(parsedData.headers)) {
            return this.createErrorResult('标题字段缺失或格式无效');
        }

        // 验证数据量限制
        const dataValidation = this.validateDataSize(parsedData);
        if (!dataValidation.isValid) {
            return dataValidation;
        }

        // 验证标题字段
        const headerValidation = this.validateHeaders(parsedData.headers);
        if (!headerValidation.isValid) {
            return headerValidation;
        }
        result.warnings.push(...headerValidation.warnings);

        // 验证数据内容
        const contentValidation = this.validateDataContent(parsedData.data, parsedData.headers);
        if (!contentValidation.isValid) {
            return contentValidation;
        }
        result.warnings.push(...contentValidation.warnings);
        result.statistics = contentValidation.statistics;

        // 更新统计信息
        result.statistics.totalRows = parsedData.data.length;
        result.statistics.totalColumns = parsedData.headers.length;

        // 检查警告数量
        if (result.warnings.length > 0) {
            result.message = `数据验证通过，但有 ${result.warnings.length} 个警告`;
        } else {
            result.message = '数据验证通过';
        }

        SmartOffice.log('info', '数据验证完成:', result.message);
        return result;

    } catch (error) {
        SmartOffice.log('error', '数据验证失败:', error);
        return this.createErrorResult('数据验证过程出错: ' + error.message);
    }
};

/**
 * @function DataValidator.prototype.validateDataSize
 * @description 验证数据大小限制
 * @param {Object} parsedData - 解析后的数据
 * @returns {Object} 验证结果
 */
DataValidator.prototype.validateDataSize = function(parsedData) {
    // 检查行数限制
    if (parsedData.data.length > this.options.maxRows) {
        return this.createErrorResult(
            `数据行数超过限制，当前 ${parsedData.data.length} 行，最大允许 ${this.options.maxRows} 行`
        );
    }

    // 检查列数限制
    if (parsedData.headers.length > this.options.maxColumns) {
        return this.createErrorResult(
            `数据列数超过限制，当前 ${parsedData.headers.length} 列，最大允许 ${this.options.maxColumns} 列`
        );
    }

    // 检查空数据
    if (parsedData.data.length === 0) {
        return this.createErrorResult('数据为空，没有有效的数据行');
    }

    if (parsedData.headers.length === 0) {
        return this.createErrorResult('没有有效的数据列');
    }

    return { isValid: true, message: '数据大小验证通过' };
};

/**
 * @function DataValidator.prototype.validateHeaders
 * @description 验证标题字段
 * @param {Array} headers - 标题数组
 * @returns {Object} 验证结果
 */
DataValidator.prototype.validateHeaders = function(headers) {
    const result = {
        isValid: true,
        message: '',
        warnings: []
    };

    const seenHeaders = new Set();
    const duplicateHeaders = [];
    const invalidHeaders = [];

    for (let i = 0; i < headers.length; i++) {
        const header = headers[i];

        // 检查字段名格式
        const fieldValidation = this.validateFieldName(header);
        if (!fieldValidation.isValid) {
            invalidHeaders.push({
                index: i,
                name: header,
                error: fieldValidation.message
            });
            continue;
        }

        // 检查重复字段名
        if (seenHeaders.has(header)) {
            duplicateHeaders.push(header);
        } else {
            seenHeaders.add(header);
        }

        // 检查保留字段名
        if (this.validationRules.fieldName.reserved.includes(header.toLowerCase())) {
            result.warnings.push(`字段名 "${header}" 是保留字，可能会导致问题`);
        }
    }

    // 处理验证结果
    if (invalidHeaders.length > 0) {
        return this.createErrorResult(
            `发现 ${invalidHeaders.length} 个无效的字段名: ${invalidHeaders.map(h => h.name).join(', ')}`
        );
    }

    if (duplicateHeaders.length > 0) {
        return this.createErrorResult(
            `发现重复的字段名: ${duplicateHeaders.join(', ')}`
        );
    }

    result.message = '标题字段验证通过';
    return result;
};

/**
 * @function DataValidator.prototype.validateDataContent
 * @description 验证数据内容
 * @param {Array} data - 数据数组
 * @param {Array} headers - 标题数组
 * @returns {Object} 验证结果
 */
DataValidator.prototype.validateDataContent = function(data, headers) {
    const result = {
        isValid: true,
        message: '',
        warnings: [],
        statistics: {
            emptyValues: 0,
            invalidValues: 0,
            dataTypes: {}
        }
    };

    // 初始化数据类型统计
    for (let i = 0; i < headers.length; i++) {
        result.statistics.dataTypes[headers[i]] = {
            string: 0,
            number: 0,
            date: 0,
            boolean: 0,
            empty: 0,
            invalid: 0
        };
    }

    // 验证每行数据
    for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
        const row = data[rowIndex];

        // 检查行数据格式
        if (!row || typeof row !== 'object') {
            result.warnings.push(`第 ${rowIndex + 1} 行数据格式无效`);
            continue;
        }

        // 验证每个字段的值
        for (let colIndex = 0; colIndex < headers.length; colIndex++) {
            const fieldName = headers[colIndex];
            const value = row[fieldName];

            // 验证单个值
            const valueValidation = this.validateDataValue(value, fieldName, rowIndex + 1);
            if (!valueValidation.isValid) {
                result.warnings.push(valueValidation.message);
                result.statistics.invalidValues++;
                result.statistics.dataTypes[fieldName].invalid++;
            } else {
                // 统计数据类型
                const dataType = this.detectDataType(value);
                if (dataType === 'empty') {
                    result.statistics.emptyValues++;
                }
                result.statistics.dataTypes[fieldName][dataType]++;
            }
        }
    }

    // 检查数据质量
    const qualityCheck = this.checkDataQuality(result.statistics, data.length, headers.length);
    result.warnings.push(...qualityCheck.warnings);

    result.message = '数据内容验证完成';
    return result;
};

/**
 * @function DataValidator.prototype.validateDataValue
 * @description 验证单个数据值
 * @param {*} value - 数据值
 * @param {string} fieldName - 字段名
 * @param {number} rowNumber - 行号
 * @returns {Object} 验证结果
 */
DataValidator.prototype.validateDataValue = function(value, fieldName, rowNumber) {
    // 检查空值
    if (value === null || value === undefined || value === '') {
        if (!this.options.allowEmptyValues && this.options.requiredFields.includes(fieldName)) {
            return {
                isValid: false,
                message: `第 ${rowNumber} 行字段 "${fieldName}" 不能为空`
            };
        }
        return { isValid: true, message: '空值验证通过' };
    }

    // 转换为字符串进行验证
    const stringValue = String(value);

    // 检查值长度
    if (stringValue.length > this.validationRules.dataValue.maxLength) {
        return {
            isValid: false,
            message: `第 ${rowNumber} 行字段 "${fieldName}" 值过长（超过 ${this.validationRules.dataValue.maxLength} 字符）`
        };
    }

    // 严格模式下的额外验证
    if (this.options.strictMode) {
        // 检查特殊字符
        if (/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(stringValue)) {
            return {
                isValid: false,
                message: `第 ${rowNumber} 行字段 "${fieldName}" 包含无效的控制字符`
            };
        }
    }

    return { isValid: true, message: '数据值验证通过' };
};

/**
 * @function DataValidator.prototype.validateFieldName
 * @description 验证字段名
 * @param {string} fieldName - 字段名
 * @returns {Object} 验证结果
 */
DataValidator.prototype.validateFieldName = function(fieldName) {
    if (!fieldName || typeof fieldName !== 'string') {
        return { isValid: false, message: '字段名不能为空' };
    }

    // 检查长度
    if (fieldName.length < this.validationRules.fieldName.minLength) {
        return { isValid: false, message: '字段名过短' };
    }

    if (fieldName.length > this.validationRules.fieldName.maxLength) {
        return { isValid: false, message: '字段名过长' };
    }

    // 检查格式
    if (!this.validationRules.fieldName.pattern.test(fieldName)) {
        return { isValid: false, message: '字段名包含无效字符' };
    }

    return { isValid: true, message: '字段名验证通过' };
};

/**
 * @function DataValidator.prototype.detectDataType
 * @description 检测数据类型
 * @param {*} value - 数据值
 * @returns {string} 数据类型
 */
DataValidator.prototype.detectDataType = function(value) {
    if (value === null || value === undefined || value === '') {
        return 'empty';
    }

    const stringValue = String(value).trim();

    // 按优先级检查数据类型 - 简化为三种核心类型：数值、日期、时间
    // 1. 数值类型（包括整数、小数、百分比、货币）
    if (this.dataTypeDetectors.number.test(stringValue)) {
        return 'number';
    }

    // 2. 时间类型（包括带日期的时间，需要在日期之前检查）
    if (this.dataTypeDetectors.time.test(stringValue)) {
        return 'time';
    }

    // 3. 日期类型
    if (this.dataTypeDetectors.date.test(stringValue)) {
        return 'date';
    }

    // 4. 其他所有类型统一作为字符串处理
    return 'string';
};

/**
 * @function DataValidator.prototype.checkDataQuality
 * @description 检查数据质量
 * @param {Object} statistics - 统计信息
 * @param {number} totalRows - 总行数
 * @param {number} totalColumns - 总列数
 * @returns {Object} 质量检查结果
 */
DataValidator.prototype.checkDataQuality = function(statistics, totalRows, totalColumns) {
    const warnings = [];

    // 计算空值比例
    const emptyValueRatio = statistics.emptyValues / (totalRows * totalColumns);
    if (emptyValueRatio > 0.5) {
        warnings.push(`数据中空值比例过高 (${Math.round(emptyValueRatio * 100)}%)，可能影响分析结果`);
    } else if (emptyValueRatio > 0.2) {
        warnings.push(`数据中空值比例较高 (${Math.round(emptyValueRatio * 100)}%)，建议检查数据质量`);
    }

    // 检查无效值比例
    const invalidValueRatio = statistics.invalidValues / (totalRows * totalColumns);
    if (invalidValueRatio > 0.1) {
        warnings.push(`数据中无效值比例过高 (${Math.round(invalidValueRatio * 100)}%)，建议清理数据`);
    }

    // 检查每列的数据类型一致性
    for (const fieldName in statistics.dataTypes) {
        const fieldStats = statistics.dataTypes[fieldName];
        const nonEmptyCount = totalRows - fieldStats.empty;

        if (nonEmptyCount > 0) {
            const dominantType = this.getDominantDataType(fieldStats);
            const dominantRatio = fieldStats[dominantType] / nonEmptyCount;

            if (dominantRatio < 0.8) {
                warnings.push(`字段 "${fieldName}" 的数据类型不一致，建议检查数据格式`);
            }
        }
    }

    return { warnings };
};

/**
 * @function DataValidator.prototype.getDominantDataType
 * @description 获取字段的主要数据类型
 * @param {Object} fieldStats - 字段统计信息
 * @returns {string} 主要数据类型
 */
DataValidator.prototype.getDominantDataType = function(fieldStats) {
    let maxCount = 0;
    let dominantType = 'string';

    // 只考虑实际的数据类型，排除统计属性
    const validTypes = ['string', 'number', 'date', 'time'];

    for (let i = 0; i < validTypes.length; i++) {
        const type = validTypes[i];
        if (fieldStats[type] && fieldStats[type] > maxCount) {
            maxCount = fieldStats[type];
            dominantType = type;
        }
    }

    return dominantType;
};

/**
 * @function DataValidator.prototype.validatePivotConfig
 * @description 验证透视表配置
 * @param {Object} config - 透视表配置对象
 * @returns {Object} 验证结果
 */
DataValidator.prototype.validatePivotConfig = function(config) {
    try {
        // 检查基本结构
        if (!config || typeof config !== 'object') {
            return this.createErrorResult('透视表配置格式无效');
        }

        // 检查必需属性
        const requiredProps = this.validationRules.pivotConfig.requiredProperties;
        for (let i = 0; i < requiredProps.length; i++) {
            const prop = requiredProps[i];
            if (!config.hasOwnProperty(prop)) {
                return this.createErrorResult(`缺少必需属性: ${prop}`);
            }
        }

        // 验证配置名称
        if (!config.name || typeof config.name !== 'string') {
            return this.createErrorResult('配置名称无效');
        }

        if (config.name.length < this.validationRules.pivotConfig.nameMinLength ||
            config.name.length > this.validationRules.pivotConfig.nameMaxLength) {
            return this.createErrorResult(
                `配置名称长度必须在 ${this.validationRules.pivotConfig.nameMinLength}-${this.validationRules.pivotConfig.nameMaxLength} 字符之间`
            );
        }

        // 验证字段配置
        const fieldValidation = this.validatePivotFields(config);
        if (!fieldValidation.isValid) {
            return fieldValidation;
        }

        return { isValid: true, message: '透视表配置验证通过' };

    } catch (error) {
        return this.createErrorResult('透视表配置验证失败: ' + error.message);
    }
};

/**
 * @function DataValidator.prototype.validatePivotFields
 * @description 验证透视表字段配置
 * @param {Object} config - 透视表配置
 * @returns {Object} 验证结果
 */
DataValidator.prototype.validatePivotFields = function(config) {
    const fieldTypes = ['rowFields', 'columnFields', 'valueFields', 'filterFields'];

    for (let i = 0; i < fieldTypes.length; i++) {
        const fieldType = fieldTypes[i];
        const fields = config[fieldType];

        // 检查字段数组格式
        if (!Array.isArray(fields)) {
            return this.createErrorResult(`${fieldType} 必须是数组格式`);
        }

        // 检查字段数量限制
        if (fields.length > this.validationRules.pivotConfig.maxFieldsPerType) {
            return this.createErrorResult(
                `${fieldType} 字段数量超过限制（最大 ${this.validationRules.pivotConfig.maxFieldsPerType} 个）`
            );
        }

        // 验证每个字段
        for (let j = 0; j < fields.length; j++) {
            const field = fields[j];
            if (!field || typeof field !== 'string') {
                return this.createErrorResult(`${fieldType} 中的字段名无效: ${field}`);
            }

            const fieldValidation = this.validateFieldName(field);
            if (!fieldValidation.isValid) {
                return this.createErrorResult(`${fieldType} 中的字段名 "${field}" 无效: ${fieldValidation.message}`);
            }
        }
    }

    // 检查值字段是否为空
    if (config.valueFields.length === 0) {
        return this.createErrorResult('至少需要选择一个值字段');
    }

    // 检查是否有重复字段
    const allFields = [
        ...config.rowFields,
        ...config.columnFields,
        ...config.valueFields,
        ...config.filterFields
    ];

    const duplicateFields = this.findDuplicates(allFields);
    if (duplicateFields.length > 0) {
        return this.createErrorResult(`发现重复的字段: ${duplicateFields.join(', ')}`);
    }

    return { isValid: true, message: '透视表字段配置验证通过' };
};

/**
 * @function DataValidator.prototype.findDuplicates
 * @description 查找数组中的重复元素
 * @param {Array} array - 要检查的数组
 * @returns {Array} 重复元素数组
 */
DataValidator.prototype.findDuplicates = function(array) {
    const seen = new Set();
    const duplicates = new Set();

    for (let i = 0; i < array.length; i++) {
        const item = array[i];
        if (seen.has(item)) {
            duplicates.add(item);
        } else {
            seen.add(item);
        }
    }

    return Array.from(duplicates);
};

/**
 * @function DataValidator.prototype.createErrorResult
 * @description 创建错误结果对象
 * @param {string} message - 错误消息
 * @returns {Object} 错误结果
 */
DataValidator.prototype.createErrorResult = function(message) {
    return {
        isValid: false,
        message: message,
        warnings: [],
        errors: [message]
    };
};

/**
 * @function DataValidator.prototype.getFieldDataTypes
 * @description 获取字段的数据类型信息
 * @param {Array} data - 数据数组
 * @param {Array} headers - 标题数组
 * @returns {Object} 字段数据类型信息
 */
DataValidator.prototype.getFieldDataTypes = function(data, headers) {
    const fieldTypes = {};

    // 初始化字段类型统计 - 简化为四种类型：字符串、数值、日期、时间
    for (let i = 0; i < headers.length; i++) {
        fieldTypes[headers[i]] = {
            string: 0,
            number: 0,
            date: 0,
            time: 0,
            empty: 0,
            total: 0,
            dominantType: 'string',
            confidence: 0
        };
    }

    // 分析每行数据
    for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
        const row = data[rowIndex];

        for (let colIndex = 0; colIndex < headers.length; colIndex++) {
            const fieldName = headers[colIndex];
            const value = row[fieldName];
            const dataType = this.detectDataType(value);

            fieldTypes[fieldName][dataType]++;
            fieldTypes[fieldName].total++;
        }
    }

    // 确定主要数据类型和置信度
    for (const fieldName in fieldTypes) {
        const stats = fieldTypes[fieldName];
        const nonEmptyTotal = stats.total - stats.empty;

        if (nonEmptyTotal > 0) {
            const dominantType = this.getDominantDataType(stats);
            const confidence = stats[dominantType] / nonEmptyTotal;

            fieldTypes[fieldName].dominantType = dominantType;
            fieldTypes[fieldName].confidence = Math.round(confidence * 100) / 100;
        }
    }

    return fieldTypes;
};

/**
 * @function DataValidator.prototype.isNumericField
 * @description 判断字段是否为数值类型
 * @param {string} fieldName - 字段名
 * @param {Object} fieldTypes - 字段类型信息
 * @returns {boolean} 是否为数值字段
 */
DataValidator.prototype.isNumericField = function(fieldName, fieldTypes) {
    if (!fieldTypes || !fieldTypes[fieldName]) {
        return false;
    }

    const fieldType = fieldTypes[fieldName];
    return fieldType.dominantType === 'number' && fieldType.confidence >= 0.8;
};

/**
 * @function DataValidator.prototype.getValidationSummary
 * @description 获取验证结果摘要
 * @param {Object} validationResult - 验证结果
 * @returns {Object} 验证摘要
 */
DataValidator.prototype.getValidationSummary = function(validationResult) {
    return {
        isValid: validationResult.isValid,
        errorCount: validationResult.errors ? validationResult.errors.length : 0,
        warningCount: validationResult.warnings ? validationResult.warnings.length : 0,
        totalRows: validationResult.statistics ? validationResult.statistics.totalRows : 0,
        totalColumns: validationResult.statistics ? validationResult.statistics.totalColumns : 0,
        emptyValues: validationResult.statistics ? validationResult.statistics.emptyValues : 0,
        invalidValues: validationResult.statistics ? validationResult.statistics.invalidValues : 0
    };
};

// 注册到全局命名空间
SmartOffice.Data.DataValidator = DataValidator;

SmartOffice.log('info', 'SmartOffice数据验证器模块初始化完成');
