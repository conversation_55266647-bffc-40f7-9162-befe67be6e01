/**
 * SmartOffice Excel解析器
 * 纯JavaScript实现的XLSX文件解析器，支持基本的Excel文件读取
 * @function ExcelParser - Excel文件解析和数据提取
 */

(function() {
    'use strict';

    /**
     * Excel解析器构造函数
     * @function ExcelParser - 创建Excel解析器实例
     */
    function ExcelParser() {
        this.eventBus = SmartOffice.Core.EventBus;
        this.dataValidator = new SmartOffice.Data.DataValidator();
        
        // 支持的文件类型
        this.supportedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];
        
        console.log('📊 Excel解析器: 初始化完成');
    }

    /**
     * 检查文件是否为支持的Excel格式
     * @function isExcelFile - 验证文件格式
     * @param {File} file - 要检查的文件对象
     * @returns {boolean} 是否为支持的Excel文件
     */
    ExcelParser.prototype.isExcelFile = function(file) {
        if (!file) {
            return false;
        }
        
        // 检查MIME类型
        if (this.supportedTypes.includes(file.type)) {
            return true;
        }
        
        // 检查文件扩展名
        const fileName = file.name.toLowerCase();
        return fileName.endsWith('.xlsx') || fileName.endsWith('.xls');
    };

    /**
     * 解析Excel文件内容（与CSV解析器兼容的接口）
     * @function parse - 解析Excel文件内容
     * @param {ArrayBuffer} content - 文件内容（ArrayBuffer格式）
     * @param {Object} options - 解析选项
     * @param {Function} options.onProgress - 进度回调
     * @param {Function} options.onComplete - 完成回调
     * @param {Function} options.onError - 错误回调
     */
    ExcelParser.prototype.parse = function(content, options) {
        const self = this;
        options = options || {};

        try {
            console.log('📊 Excel解析器: 开始解析内容');

            // 模拟进度更新
            if (options.onProgress) {
                options.onProgress(0.1);
            }

            // 使用setTimeout模拟异步处理，避免阻塞UI
            setTimeout(function() {
                try {
                    if (options.onProgress) {
                        options.onProgress(0.3);
                    }

                    const workbook = self.parseWorkbook(content);

                    if (options.onProgress) {
                        options.onProgress(0.6);
                    }

                    // 获取第一个工作表的数据（异步）
                    self.getFirstWorksheet(workbook)
                        .then(function(worksheet) {
                            const data = self.extractData(worksheet);

                            if (options.onProgress) {
                                options.onProgress(0.8);
                            }

                            // 验证和标准化数据
                            const result = self.processData(data, 'excel_file.xlsx');

                            if (options.onProgress) {
                                options.onProgress(1.0);
                            }

                            console.log('✅ Excel解析器: 解析完成，共', result.data.length, '行数据');

                            if (options.onComplete) {
                                options.onComplete(result);
                            }
                        })
                        .catch(function(error) {
                            console.error('❌ Excel解析器: 工作表解析失败', error);
                            if (options.onError) {
                                options.onError(error);
                            }
                        });

                } catch (error) {
                    console.error('❌ Excel解析器: 解析失败', error);
                    if (options.onError) {
                        options.onError(error);
                    }
                }
            }, 100);

        } catch (error) {
            console.error('❌ Excel解析器: 初始化失败', error);
            if (options.onError) {
                options.onError(error);
            }
        }
    };

    /**
     * 解析Excel文件
     * @function parseFile - 解析Excel文件并提取数据
     * @param {File} file - Excel文件对象
     * @returns {Promise} 解析结果Promise
     */
    ExcelParser.prototype.parseFile = function(file) {
        return new Promise(function(resolve, reject) {
            if (!this.isExcelFile(file)) {
                reject(new Error('不支持的文件格式，请选择.xlsx或.xls文件'));
                return;
            }

            console.log('📊 Excel解析器: 开始解析文件', file.name);
            this.eventBus.emit('excel:parse:start', { file: file });

            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    const arrayBuffer = e.target.result;
                    const workbook = this.parseWorkbook(arrayBuffer);

                    // 获取第一个工作表的数据（异步）
                    this.getFirstWorksheet(workbook)
                        .then(function(worksheet) {
                            const data = this.extractData(worksheet);

                            // 验证和标准化数据
                            const result = this.processData(data, file.name);

                            console.log('✅ Excel解析器: 解析完成，共', result.data.length, '行数据');
                            this.eventBus.emit('excel:parse:complete', result);

                            resolve(result);
                        }.bind(this))
                        .catch(function(error) {
                            console.error('❌ Excel解析器: 工作表解析失败', error);
                            this.eventBus.emit('excel:parse:error', { error: error });
                            reject(error);
                        }.bind(this));
                } catch (error) {
                    console.error('❌ Excel解析器: 解析失败', error);
                    this.eventBus.emit('excel:parse:error', { error: error });
                    reject(error);
                }
            }.bind(this);

            reader.onerror = function() {
                const error = new Error('文件读取失败');
                console.error('❌ Excel解析器: 文件读取失败');
                this.eventBus.emit('excel:parse:error', { error: error });
                reject(error);
            }.bind(this);

            reader.readAsArrayBuffer(file);
        }.bind(this));
    };

    /**
     * 解析工作簿结构（简化版ZIP解析）
     * @function parseWorkbook - 解析Excel工作簿结构
     * @param {ArrayBuffer} arrayBuffer - 文件数据
     * @returns {Object} 工作簿对象
     */
    ExcelParser.prototype.parseWorkbook = function(arrayBuffer) {
        // 这是一个简化的实现，实际的XLSX解析需要完整的ZIP解析
        // 为了保持零依赖，这里实现一个基础版本
        
        const uint8Array = new Uint8Array(arrayBuffer);
        
        // 检查ZIP文件头
        if (uint8Array[0] !== 0x50 || uint8Array[1] !== 0x4B) {
            throw new Error('无效的Excel文件格式');
        }
        
        // 简化的工作簿对象
        return {
            arrayBuffer: arrayBuffer,
            uint8Array: uint8Array,
            worksheets: ['Sheet1'] // 简化版本只支持第一个工作表
        };
    };

    /**
     * 获取第一个工作表
     * @function getFirstWorksheet - 获取第一个工作表数据
     * @param {Object} workbook - 工作簿对象
     * @returns {Promise<Object>} 工作表对象的Promise
     */
    ExcelParser.prototype.getFirstWorksheet = function(workbook) {
        // 实现：从ZIP中提取工作表数据
        return this.extractSimpleData(workbook.uint8Array)
            .then(function(data) {
                return {
                    name: 'Sheet1',
                    data: data
                };
            });
    };

    /**
     * 从工作表提取数据
     * @function extractData - 从工作表提取结构化数据
     * @param {Object} worksheet - 工作表对象
     * @returns {Array} 数据数组
     */
    ExcelParser.prototype.extractData = function(worksheet) {
        return worksheet.data || [];
    };

    /**
     * 处理和标准化数据
     * @function processData - 处理解析后的数据
     * @param {Array} rawData - 原始数据数组
     * @param {string} fileName - 文件名
     * @returns {Object} 处理后的数据对象
     */
    ExcelParser.prototype.processData = function(rawData, fileName) {
        if (!rawData || rawData.length === 0) {
            throw new Error('Excel文件中没有找到数据');
        }

        // 第一行作为表头
        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // 验证表头
        if (!headers || headers.length === 0) {
            throw new Error('Excel文件中没有找到表头');
        }

        // 转换为对象数组
        const data = dataRows.map(function(row, index) {
            const rowData = {};
            headers.forEach(function(header, colIndex) {
                const value = row[colIndex];
                rowData[header] = value !== undefined ? String(value).trim() : '';
            });
            return rowData;
        });

        // 过滤空行
        const filteredData = data.filter(function(row) {
            return Object.values(row).some(function(value) {
                return value && value.trim() !== '';
            });
        });

        // 构建解析结果对象
        const parseResult = {
            fileName: fileName,
            fileType: 'excel',
            headers: headers,
            data: filteredData,
            rowCount: filteredData.length,
            columnCount: headers.length,
            statistics: this.generateStatistics(filteredData, headers),
            parseTime: new Date().toISOString()
        };

        // 数据验证和类型检测 - 使用正确的方法名
        const validationResult = this.dataValidator.validateParsedData(parseResult);

        // 合并验证结果
        parseResult.isValid = validationResult.isValid;
        parseResult.errors = validationResult.errors || [];
        parseResult.warnings = validationResult.warnings || [];
        parseResult.validationMessage = validationResult.message || '';

        return parseResult;
    };

    /**
     * 生成数据统计信息
     * @function generateStatistics - 生成数据统计
     * @param {Array} data - 数据数组
     * @param {Array} headers - 表头数组
     * @returns {Object} 统计信息对象
     */
    ExcelParser.prototype.generateStatistics = function(data, headers) {
        const stats = {};
        
        headers.forEach(function(header) {
            const values = data.map(function(row) {
                return row[header];
            }).filter(function(value) {
                return value && value.trim() !== '';
            });
            
            stats[header] = {
                totalCount: data.length,
                validCount: values.length,
                emptyCount: data.length - values.length,
                uniqueCount: new Set(values).size,
                sampleValues: values.slice(0, 5)
            };
        });
        
        return stats;
    };

    /**
     * 获取支持的文件类型
     * @function getSupportedTypes - 获取支持的文件类型列表
     * @returns {Array} 支持的MIME类型数组
     */
    ExcelParser.prototype.getSupportedTypes = function() {
        return this.supportedTypes.slice();
    };

    /**
     * 从Excel二进制数据中提取实际数据
     * @function extractSimpleData - 解析XLSX文件并提取工作表数据
     * @param {Uint8Array} uint8Array - Excel文件的二进制数据
     * @returns {Array} 提取的数据数组（二维数组格式）
     */
    ExcelParser.prototype.extractSimpleData = function(uint8Array) {
        console.log('📊 Excel解析器: 开始解析XLSX文件内容，文件大小:', uint8Array.length, '字节');

        try {
            // 1. 解析ZIP文件结构
            const zipEntries = this.parseZipFile(uint8Array);
            console.log('📦 找到', zipEntries.length, '个ZIP条目:');
            zipEntries.forEach(function(entry, index) {
                console.log(`  ${index + 1}. ${entry.fileName} (${entry.uncompressedSize} 字节)`);
            });

            // 2. 查找工作表XML文件
            const worksheetEntry = this.findWorksheetEntry(zipEntries);
            if (!worksheetEntry) {
                console.error('❌ 未找到工作表文件，可用文件列表:');
                zipEntries.forEach(function(entry) {
                    console.error(`  - ${entry.fileName}`);
                });
                throw new Error('未找到工作表数据，可能不是有效的Excel文件');
            }
            console.log('📋 找到工作表文件:', worksheetEntry.fileName);

            // 3. 提取工作表XML内容
            const self = this;
            return this.extractFileContent(uint8Array, worksheetEntry)
                .then(function(worksheetXML) {
                    console.log('📄 工作表XML长度:', worksheetXML.length, '字符');
                    console.log('📄 XML内容预览:', worksheetXML.substring(0, 500) + '...');

                    // 4. 查找并解析共享字符串（如果存在）
                    return self.extractSharedStrings(uint8Array, zipEntries)
                        .then(function(sharedStrings) {
                            console.log('📝 共享字符串数量:', sharedStrings.length);
                            if (sharedStrings.length > 0) {
                                console.log('📝 共享字符串预览:', sharedStrings.slice(0, 5));
                            }

                            // 5. 解析工作表数据
                            const data = self.parseWorksheetData(worksheetXML, sharedStrings);

                            console.log('✅ Excel解析完成，提取到', data.length, '行数据');
                            if (data.length > 0) {
                                console.log('📊 数据预览:', data.slice(0, 3));
                            }
                            return data;
                        });
                });

        } catch (error) {
            console.error('❌ Excel解析失败:', error.message);
            console.error('❌ 错误堆栈:', error.stack);
            // 如果解析失败，返回错误信息而不是演示数据
            throw new Error('Excel文件解析失败: ' + error.message);
        }
    };

    /**
     * 解析ZIP文件结构
     * @function parseZipFile - 解析ZIP文件的中央目录
     * @param {Uint8Array} uint8Array - ZIP文件的二进制数据
     * @returns {Array} ZIP条目数组
     */
    ExcelParser.prototype.parseZipFile = function(uint8Array) {
        // 查找中央目录结束记录
        const eocdOffset = this.findEndOfCentralDirectory(uint8Array);
        if (eocdOffset === -1) {
            throw new Error('无效的ZIP文件格式');
        }

        // 读取中央目录信息
        const view = new DataView(uint8Array.buffer);
        const centralDirOffset = view.getUint32(eocdOffset + 16, true);
        const entryCount = view.getUint16(eocdOffset + 10, true);

        // 解析中央目录条目
        const entries = [];
        let offset = centralDirOffset;

        for (let i = 0; i < entryCount; i++) {
            const entry = this.parseCentralDirectoryEntry(uint8Array, offset);
            entries.push(entry);
            offset += entry.totalSize;
        }

        return entries;
    };

    /**
     * 查找ZIP文件的中央目录结束记录
     * @function findEndOfCentralDirectory - 查找EOCD记录
     * @param {Uint8Array} uint8Array - ZIP文件数据
     * @returns {number} EOCD记录的偏移位置，-1表示未找到
     */
    ExcelParser.prototype.findEndOfCentralDirectory = function(uint8Array) {
        // EOCD签名: 0x06054b50
        const signature = 0x06054b50;
        const view = new DataView(uint8Array.buffer);

        // 从文件末尾向前搜索EOCD记录
        for (let i = uint8Array.length - 22; i >= 0; i--) {
            if (view.getUint32(i, true) === signature) {
                return i;
            }
        }

        return -1;
    };

    /**
     * 解析中央目录条目
     * @function parseCentralDirectoryEntry - 解析单个中央目录条目
     * @param {Uint8Array} uint8Array - ZIP文件数据
     * @param {number} offset - 条目起始偏移
     * @returns {Object} 解析后的条目信息
     */
    ExcelParser.prototype.parseCentralDirectoryEntry = function(uint8Array, offset) {
        const view = new DataView(uint8Array.buffer);

        // 读取文件名长度
        const fileNameLength = view.getUint16(offset + 28, true);
        const extraFieldLength = view.getUint16(offset + 30, true);
        const commentLength = view.getUint16(offset + 32, true);

        // 读取文件名
        const fileNameBytes = uint8Array.slice(offset + 46, offset + 46 + fileNameLength);
        const fileName = new TextDecoder('utf-8').decode(fileNameBytes);

        // 读取其他重要信息
        const compressedSize = view.getUint32(offset + 20, true);
        const uncompressedSize = view.getUint32(offset + 24, true);
        const localHeaderOffset = view.getUint32(offset + 42, true);
        const compressionMethod = view.getUint16(offset + 10, true);

        return {
            fileName: fileName,
            compressedSize: compressedSize,
            uncompressedSize: uncompressedSize,
            localHeaderOffset: localHeaderOffset,
            compressionMethod: compressionMethod,
            totalSize: 46 + fileNameLength + extraFieldLength + commentLength
        };
    };

    /**
     * 查找工作表XML文件条目
     * @function findWorksheetEntry - 在ZIP条目中查找工作表文件
     * @param {Array} zipEntries - ZIP文件条目数组
     * @returns {Object|null} 工作表条目对象，未找到返回null
     */
    ExcelParser.prototype.findWorksheetEntry = function(zipEntries) {
        console.log('🔍 查找工作表文件...');

        // 优先查找的工作表文件名列表
        const preferredNames = [
            'xl/worksheets/sheet1.xml',
            'xl/worksheets/sheet.xml',
            'xl/worksheets/Sheet1.xml'
        ];

        // 首先尝试查找常见的工作表文件名
        for (let i = 0; i < preferredNames.length; i++) {
            const preferredName = preferredNames[i];
            for (let j = 0; j < zipEntries.length; j++) {
                const entry = zipEntries[j];
                if (entry.fileName === preferredName) {
                    console.log('✅ 找到首选工作表文件:', preferredName);
                    return entry;
                }
            }
        }

        // 查找所有工作表文件
        const worksheetEntries = [];
        for (let i = 0; i < zipEntries.length; i++) {
            const entry = zipEntries[i];
            if (entry.fileName.startsWith('xl/worksheets/') && entry.fileName.endsWith('.xml')) {
                worksheetEntries.push(entry);
            }
        }

        console.log('📋 找到', worksheetEntries.length, '个工作表文件:');
        worksheetEntries.forEach(function(entry, index) {
            console.log(`  ${index + 1}. ${entry.fileName} (${entry.uncompressedSize} 字节)`);
        });

        if (worksheetEntries.length > 0) {
            // 选择第一个工作表文件
            const selectedEntry = worksheetEntries[0];
            console.log('✅ 选择工作表文件:', selectedEntry.fileName);
            return selectedEntry;
        }

        // 如果没有找到标准的工作表文件，查找其他可能的XML文件
        console.log('⚠️ 未找到标准工作表文件，查找其他XML文件...');
        const xmlEntries = [];
        for (let i = 0; i < zipEntries.length; i++) {
            const entry = zipEntries[i];
            if (entry.fileName.endsWith('.xml') && entry.uncompressedSize > 100) {
                xmlEntries.push(entry);
            }
        }

        console.log('📄 找到', xmlEntries.length, '个XML文件:');
        xmlEntries.forEach(function(entry, index) {
            console.log(`  ${index + 1}. ${entry.fileName} (${entry.uncompressedSize} 字节)`);
        });

        // 查找可能包含工作表数据的XML文件
        for (let i = 0; i < xmlEntries.length; i++) {
            const entry = xmlEntries[i];
            if (entry.fileName.includes('sheet') ||
                entry.fileName.includes('worksheet') ||
                entry.fileName.includes('xl/')) {
                console.log('🎯 尝试使用可能的工作表文件:', entry.fileName);
                return entry;
            }
        }

        console.error('❌ 未找到任何工作表文件');
        return null;
    };

    /**
     * 提取ZIP文件中的文件内容
     * @function extractFileContent - 提取指定文件的内容
     * @param {Uint8Array} uint8Array - ZIP文件数据
     * @param {Object} entry - 文件条目信息
     * @returns {string} 文件内容（文本格式）
     */
    ExcelParser.prototype.extractFileContent = function(uint8Array, entry) {
        console.log('📄 提取文件内容:', entry.fileName);
        console.log('📊 文件信息: 压缩大小', entry.compressedSize, '未压缩大小', entry.uncompressedSize, '压缩方法', entry.compressionMethod);

        const view = new DataView(uint8Array.buffer);

        // 读取本地文件头
        const localHeaderOffset = entry.localHeaderOffset;
        console.log('📍 本地文件头偏移:', localHeaderOffset);

        // 验证本地文件头签名
        const localSignature = view.getUint32(localHeaderOffset, true);
        if (localSignature !== 0x04034b50) {
            throw new Error('无效的本地文件头签名: 0x' + localSignature.toString(16));
        }

        const fileNameLength = view.getUint16(localHeaderOffset + 26, true);
        const extraFieldLength = view.getUint16(localHeaderOffset + 28, true);

        console.log('📏 文件名长度:', fileNameLength, '额外字段长度:', extraFieldLength);

        // 计算文件数据的起始位置
        const dataOffset = localHeaderOffset + 30 + fileNameLength + extraFieldLength;
        console.log('📍 文件数据偏移:', dataOffset);

        // 验证数据偏移是否有效
        if (dataOffset + entry.compressedSize > uint8Array.length) {
            throw new Error('文件数据超出ZIP文件范围');
        }

        // 提取文件数据
        const fileData = uint8Array.slice(dataOffset, dataOffset + entry.compressedSize);
        console.log('📦 提取的文件数据大小:', fileData.length, '字节');

        // 如果文件未压缩，直接转换为文本
        if (entry.compressionMethod === 0) {
            console.log('📄 文件未压缩，直接解码');
            const content = new TextDecoder('utf-8').decode(fileData);
            console.log('📝 解码后内容长度:', content.length, '字符');
            return Promise.resolve(content);
        }

        // 对于压缩文件，尝试简单的解压缩
        console.log('🗜️ 文件已压缩，压缩方法:', entry.compressionMethod);

        if (entry.compressionMethod === 8) {
            // DEFLATE压缩 - 使用浏览器原生API
            console.log('🔧 尝试使用浏览器原生解压缩API...');

            try {
                // 尝试使用CompressionStream API（如果可用）
                if (typeof DecompressionStream !== 'undefined') {
                    return this.decompressWithNativeAPI(fileData);
                }
            } catch (error) {
                console.warn('⚠️ 原生API解压缩失败:', error.message);
            }

            // 如果原生API不可用，尝试pako库（如果已加载）
            if (typeof pako !== 'undefined') {
                try {
                    console.log('🔧 尝试使用pako库解压缩...');
                    const decompressed = pako.inflate(fileData, { to: 'string' });
                    console.log('✅ pako解压缩成功，内容长度:', decompressed.length);
                    return Promise.resolve(decompressed);
                } catch (error) {
                    console.warn('⚠️ pako解压缩失败:', error.message);
                }
            }

            // 最后尝试简单的解压缩方法
            console.log('🔧 尝试简单的DEFLATE解压缩...');
            try {
                const decompressed = this.simpleDeflateDecompress(fileData);
                if (decompressed) {
                    console.log('✅ 简单解压缩成功，内容长度:', decompressed.length);
                    return Promise.resolve(decompressed);
                }
            } catch (error) {
                console.warn('⚠️ 简单解压缩失败:', error.message);
            }

            // 如果所有方法都失败，返回错误
            console.error('❌ 所有解压缩方法都失败了');
            return Promise.reject(new Error('无法解压缩DEFLATE格式的文件。请确保文件未损坏，或考虑使用未压缩的Excel文件。'));
        }

        // 其他压缩方法
        return Promise.reject(new Error('不支持的压缩方法: ' + entry.compressionMethod));
    };

    /**
     * 提取共享字符串表
     * @function extractSharedStrings - 提取并解析共享字符串
     * @param {Uint8Array} uint8Array - ZIP文件数据
     * @param {Array} zipEntries - ZIP文件条目数组
     * @returns {Promise<Array>} 共享字符串数组的Promise
     */
    ExcelParser.prototype.extractSharedStrings = function(uint8Array, zipEntries) {
        // 查找共享字符串文件
        const sharedStringsEntry = zipEntries.find(function(entry) {
            return entry.fileName === 'xl/sharedStrings.xml';
        });

        if (!sharedStringsEntry) {
            console.log('📄 未找到共享字符串文件，使用直接值');
            return Promise.resolve([]);
        }

        const self = this;
        return this.extractFileContent(uint8Array, sharedStringsEntry)
            .then(function(xmlContent) {
                return self.parseSharedStringsXML(xmlContent);
            })
            .catch(function(error) {
                console.warn('⚠️ 共享字符串解析失败:', error.message);
                return [];
            });
    };

    /**
     * 解析共享字符串XML
     * @function parseSharedStringsXML - 解析共享字符串XML内容
     * @param {string} xmlContent - XML内容
     * @returns {Array} 字符串数组
     */
    ExcelParser.prototype.parseSharedStringsXML = function(xmlContent) {
        const strings = [];

        // 使用正则表达式提取<t>标签中的文本
        const regex = /<t[^>]*>(.*?)<\/t>/g;
        let match;

        while ((match = regex.exec(xmlContent)) !== null) {
            strings.push(match[1]);
        }

        return strings;
    };

    /**
     * 解析工作表数据
     * @function parseWorksheetData - 解析工作表XML并提取数据
     * @param {string} xmlContent - 工作表XML内容
     * @param {Array} sharedStrings - 共享字符串数组
     * @returns {Array} 二维数据数组
     */
    ExcelParser.prototype.parseWorksheetData = function(xmlContent, sharedStrings) {
        console.log('🔍 开始解析工作表数据...');
        const cellData = {};
        let cellCount = 0;

        // 多种正则表达式模式，以支持不同的Excel XML格式
        const cellPatterns = [
            // 标准格式：<c r="A1" t="s"><v>0</v></c> - 修复t属性捕获
            /<c\s+r="([A-Z]+\d+)"(?:[^>]*\s+t="([^"]*)")?[^>]*>\s*<v>(.*?)<\/v>\s*<\/c>/g,
            // 简化格式：<c r="A1"><v>123</v></c>
            /<c\s+r="([A-Z]+\d+)"[^>]*>\s*<v>(.*?)<\/v>/g,
            // 内联字符串格式：<c r="A1" t="inlineStr"><is><t>文本</t></is></c>
            /<c\s+r="([A-Z]+\d+)"[^>]*t="inlineStr"[^>]*>\s*<is>\s*<t>(.*?)<\/t>\s*<\/is>/g,
            // 公式格式：<c r="A1"><f>SUM(A1:A10)</f><v>100</v></c>
            /<c\s+r="([A-Z]+\d+)"[^>]*>\s*<f[^>]*>.*?<\/f>\s*<v>(.*?)<\/v>/g
        ];

        console.log('🔍 尝试', cellPatterns.length, '种单元格匹配模式...');

        // 尝试每种模式
        cellPatterns.forEach(function(pattern, patternIndex) {
            console.log(`🔍 尝试模式 ${patternIndex + 1}:`, pattern.source);
            let match;
            let patternMatches = 0;

            // 重置正则表达式的lastIndex
            pattern.lastIndex = 0;

            while ((match = pattern.exec(xmlContent)) !== null) {
                const cellRef = match[1];  // 如 "A1", "B2"
                let cellValue;
                let cellType = null;

                if (patternIndex === 0) {
                    // 标准格式：r, t, v
                    cellType = match[2];
                    cellValue = match[3];
                } else if (patternIndex === 1) {
                    // 简化格式：r, v
                    cellValue = match[2];
                } else if (patternIndex === 2) {
                    // 内联字符串：r, 文本内容
                    cellValue = match[2];
                    cellType = 'inlineStr';
                } else if (patternIndex === 3) {
                    // 公式格式：r, 计算值
                    cellValue = match[2];
                }

                if (cellValue !== undefined && cellValue !== '') {
                    let value = cellValue;

                    // 调试：显示前几个单元格的详细信息
                    if (cellCount < 10) {
                        console.log(`🔍 单元格 ${cellRef}: 类型="${cellType}", 值="${cellValue}"`);
                    }

                    // 如果是共享字符串类型，从共享字符串表中获取实际值
                    if (cellType === 's' && sharedStrings.length > 0) {
                        const index = parseInt(cellValue, 10);
                        if (index >= 0 && index < sharedStrings.length) {
                            value = sharedStrings[index];
                            if (cellCount < 10) {
                                console.log(`🔄 共享字符串转换: 索引${index} -> "${value}"`);
                            }
                        } else {
                            console.warn('⚠️ 共享字符串索引超出范围:', index, '最大索引:', sharedStrings.length - 1);
                            value = cellValue; // 使用原始值
                        }
                    }

                    // 避免重复添加相同的单元格
                    if (!cellData[cellRef]) {
                        cellData[cellRef] = value;
                        cellCount++;
                        patternMatches++;
                    }
                }
            }

            console.log(`📊 模式 ${patternIndex + 1} 匹配到 ${patternMatches} 个单元格`);
        });

        console.log('📊 总共提取到', cellCount, '个单元格数据');
        console.log('📊 单元格引用示例:', Object.keys(cellData).slice(0, 10));

        if (cellCount === 0) {
            console.warn('⚠️ 未找到任何单元格数据，尝试查找其他数据模式...');

            // 尝试查找行数据模式
            const rowPattern = /<row[^>]*>(.*?)<\/row>/g;
            let rowMatch;
            let rowCount = 0;

            while ((rowMatch = rowPattern.exec(xmlContent)) !== null) {
                rowCount++;
                if (rowCount <= 3) {
                    console.log(`📋 行 ${rowCount} 内容:`, rowMatch[1].substring(0, 200) + '...');
                }
            }

            console.log('📋 找到', rowCount, '行数据');

            if (rowCount === 0) {
                console.error('❌ XML中没有找到行数据，可能是空工作表或格式不支持');
                console.log('📄 XML结构分析:');
                console.log('  - 是否包含<worksheet>:', xmlContent.includes('<worksheet'));
                console.log('  - 是否包含<sheetData>:', xmlContent.includes('<sheetData'));
                console.log('  - 是否包含<row>:', xmlContent.includes('<row'));
                console.log('  - 是否包含<c>:', xmlContent.includes('<c'));
            }
        }

        // 将单元格数据转换为二维数组
        return this.convertCellDataToArray(cellData);
    };

    /**
     * 将单元格数据转换为二维数组
     * @function convertCellDataToArray - 将单元格引用数据转换为二维数组
     * @param {Object} cellData - 单元格数据对象，键为单元格引用（如A1），值为单元格值
     * @returns {Array} 二维数据数组
     */
    ExcelParser.prototype.convertCellDataToArray = function(cellData) {
        console.log('🔄 开始转换单元格数据为二维数组...');
        console.log('📊 输入的单元格数据数量:', Object.keys(cellData).length);

        if (Object.keys(cellData).length === 0) {
            console.error('❌ 单元格数据为空，无法转换为二维数组');
            throw new Error('工作表中没有找到数据');
        }

        // 解析所有单元格引用，找出数据范围
        let maxRow = 0;
        let maxCol = 0;
        let minRow = Infinity;
        let minCol = Infinity;
        const cellRefs = Object.keys(cellData);

        console.log('📋 单元格引用列表:', cellRefs.slice(0, 20)); // 显示前20个

        cellRefs.forEach(function(cellRef) {
            try {
                const parsed = this.parseCellReference(cellRef);
                maxRow = Math.max(maxRow, parsed.row);
                maxCol = Math.max(maxCol, parsed.col);
                minRow = Math.min(minRow, parsed.row);
                minCol = Math.min(minCol, parsed.col);
            } catch (error) {
                console.warn('⚠️ 无法解析单元格引用:', cellRef, error.message);
            }
        }.bind(this));

        console.log('📐 数据范围: 行', minRow, '-', maxRow, '列', minCol, '-', maxCol);

        // 如果没有有效的单元格引用
        if (maxRow === 0 || maxCol === 0) {
            console.error('❌ 没有找到有效的单元格引用');
            throw new Error('工作表中没有找到有效的数据格式');
        }

        // 创建二维数组（从第1行第1列开始）
        const data = [];
        let nonEmptyRows = 0;

        for (let row = 1; row <= maxRow; row++) {
            const rowData = [];
            let hasData = false;

            for (let col = 1; col <= maxCol; col++) {
                const cellRef = this.getCellReference(row, col);
                const cellValue = cellData[cellRef] || '';
                rowData.push(cellValue);

                if (cellValue && cellValue.toString().trim() !== '') {
                    hasData = true;
                }
            }

            data.push(rowData);
            if (hasData) {
                nonEmptyRows++;
            }
        }

        console.log('✅ 转换完成: 生成', data.length, '行', maxCol, '列的数组');
        console.log('📊 非空行数:', nonEmptyRows);

        // 显示前几行数据作为预览
        if (data.length > 0) {
            console.log('📄 数据预览:');
            data.slice(0, 3).forEach(function(row, index) {
                console.log(`  行 ${index + 1}:`, row.slice(0, 5)); // 显示前5列
            });
        }

        // 如果所有行都是空的，提供更详细的错误信息
        if (nonEmptyRows === 0) {
            console.warn('⚠️ 所有行都是空的，可能是格式化的空工作表');
            console.log('📊 原始单元格数据示例:');
            Object.keys(cellData).slice(0, 5).forEach(function(key) {
                console.log(`  ${key}: "${cellData[key]}"`);
            });
        }

        return data;
    };

    /**
     * 解析单元格引用
     * @function parseCellReference - 解析单元格引用（如A1）为行列数字
     * @param {string} cellRef - 单元格引用（如"A1", "B2"）
     * @returns {Object} 包含行列信息的对象
     */
    ExcelParser.prototype.parseCellReference = function(cellRef) {
        const match = cellRef.match(/^([A-Z]+)(\d+)$/);
        if (!match) {
            throw new Error('无效的单元格引用: ' + cellRef);
        }

        const colStr = match[1];
        const rowStr = match[2];

        // 将列字母转换为数字（A=1, B=2, ..., Z=26, AA=27, ...）
        let col = 0;
        for (let i = 0; i < colStr.length; i++) {
            col = col * 26 + (colStr.charCodeAt(i) - 64);
        }

        const row = parseInt(rowStr, 10);

        return { row: row, col: col };
    };

    /**
     * 生成单元格引用
     * @function getCellReference - 根据行列数字生成单元格引用
     * @param {number} row - 行号（从1开始）
     * @param {number} col - 列号（从1开始）
     * @returns {string} 单元格引用（如"A1"）
     */
    ExcelParser.prototype.getCellReference = function(row, col) {
        let colStr = '';
        let tempCol = col;

        while (tempCol > 0) {
            tempCol--;
            colStr = String.fromCharCode(65 + (tempCol % 26)) + colStr;
            tempCol = Math.floor(tempCol / 26);
        }

        return colStr + row;
    };

    /**
     * 使用浏览器原生API解压缩
     * @function decompressWithNativeAPI - 使用DecompressionStream解压缩
     * @param {Uint8Array} compressedData - 压缩的数据
     * @returns {Promise<string>} 解压缩后的字符串Promise
     */
    ExcelParser.prototype.decompressWithNativeAPI = function(compressedData) {
        return new Promise(function(resolve, reject) {
            try {
                console.log('🔧 使用DecompressionStream解压缩...');

                // 创建解压缩流
                const stream = new DecompressionStream('deflate-raw');
                const writer = stream.writable.getWriter();
                const reader = stream.readable.getReader();

                // 写入压缩数据
                writer.write(compressedData);
                writer.close();

                // 读取解压缩的数据
                const chunks = [];

                function readChunk() {
                    return reader.read().then(function(result) {
                        if (result.done) {
                            // 合并所有数据块
                            const totalLength = chunks.reduce(function(sum, chunk) {
                                return sum + chunk.length;
                            }, 0);

                            const combined = new Uint8Array(totalLength);
                            let offset = 0;
                            chunks.forEach(function(chunk) {
                                combined.set(chunk, offset);
                                offset += chunk.length;
                            });

                            // 转换为字符串
                            const decompressed = new TextDecoder('utf-8').decode(combined);
                            console.log('✅ 原生API解压缩成功，内容长度:', decompressed.length);
                            resolve(decompressed);
                        } else {
                            chunks.push(new Uint8Array(result.value));
                            return readChunk();
                        }
                    });
                }

                readChunk().catch(reject);

            } catch (error) {
                console.error('❌ 原生API解压缩失败:', error.message);
                reject(error);
            }
        });
    };

    /**
     * 简单的DEFLATE解压缩实现
     * @function simpleDeflateDecompress - 尝试简单的DEFLATE解压缩
     * @param {Uint8Array} compressedData - 压缩的数据
     * @returns {string|null} 解压缩后的字符串，失败返回null
     */
    ExcelParser.prototype.simpleDeflateDecompress = function(compressedData) {
        try {
            // 这是一个非常简化的实现，只能处理某些特定情况
            // 实际的DEFLATE解压缩需要完整的算法实现

            console.log('🔧 尝试简化的DEFLATE解压缩...');

            // 检查是否是未压缩的块（DEFLATE格式的特殊情况）
            if (compressedData.length > 5) {
                // 查找未压缩块的标识 (BFINAL=1, BTYPE=00)
                for (let i = 0; i < Math.min(compressedData.length - 5, 100); i++) {
                    const byte = compressedData[i];
                    // 检查是否是未压缩块的开始
                    if ((byte & 0x07) === 0x01) { // BFINAL=1, BTYPE=00
                        console.log('🔍 找到可能的未压缩块，位置:', i);

                        // 尝试读取长度信息
                        if (i + 4 < compressedData.length) {
                            const len = compressedData[i + 1] | (compressedData[i + 2] << 8);
                            const nlen = compressedData[i + 3] | (compressedData[i + 4] << 8);

                            // 验证长度的补码
                            if ((len ^ nlen) === 0xFFFF && i + 5 + len <= compressedData.length) {
                                console.log('✅ 找到有效的未压缩块，长度:', len);
                                const uncompressed = compressedData.slice(i + 5, i + 5 + len);
                                return new TextDecoder('utf-8').decode(uncompressed);
                            }
                        }
                    }
                }
            }

            // 如果没有找到未压缩块，尝试其他简单方法
            console.log('⚠️ 未找到未压缩块，尝试其他方法...');

            // 尝试跳过DEFLATE头部并直接解码
            for (let skipBytes = 0; skipBytes <= 10; skipBytes++) {
                if (skipBytes >= compressedData.length) break;

                try {
                    const testData = compressedData.slice(skipBytes);
                    const decoded = new TextDecoder('utf-8', { fatal: false }).decode(testData);

                    // 检查是否包含XML标记
                    if (decoded.includes('<?xml') || decoded.includes('<worksheet') ||
                        decoded.includes('<c r=') || decoded.includes('<row')) {
                        console.log('✅ 跳过', skipBytes, '字节后找到有效XML');
                        return decoded;
                    }
                } catch (e) {
                    // 继续尝试
                }
            }

            console.log('❌ 简单解压缩方法都失败了');
            return null;

        } catch (error) {
            console.error('❌ 简单解压缩出错:', error.message);
            return null;
        }
    };

    // 注册到全局命名空间
    if (!SmartOffice.Parsers) {
        SmartOffice.Parsers = {};
    }
    SmartOffice.Parsers.ExcelParser = ExcelParser;

    console.log('📊 Excel解析器: 模块加载完成');

})();
