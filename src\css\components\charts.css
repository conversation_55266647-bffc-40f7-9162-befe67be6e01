/**
 * 图表组件样式
 * iOS风格的图表容器和交互样式
 */

/* 图表容器 */
.chart-container {
    background: #ffffff;
    border-radius: 12px;
    padding: 16px;
    margin: 16px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e5ea;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0;
}

.chart-subtitle {
    font-size: 14px;
    color: #8e8e93;
    margin: 4px 0 0 0;
}

.chart-actions {
    display: flex;
    gap: 8px;
}

.chart-action-button {
    background: #f2f2f7;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    color: #007aff;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-action-button:hover {
    background: #e5e5ea;
}

.chart-action-button:active {
    background: #d1d1d6;
    transform: scale(0.95);
}

/* 图表画布 */
.chart-canvas-container {
    position: relative;
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-canvas {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
}

/* 图表类型选择器 */
.chart-type-selector {
    display: flex;
    background: #f2f2f7;
    border-radius: 10px;
    padding: 4px;
    margin-bottom: 16px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.chart-type-option {
    flex: 1;
    min-width: 80px;
    background: transparent;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    color: #8e8e93;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.chart-type-option.active {
    background: #ffffff;
    color: #007aff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-type-icon {
    font-size: 18px;
}

.chart-type-label {
    font-size: 12px;
}

/* 图表配置面板 */
.chart-config-panel {
    background: #f2f2f7;
    border-radius: 12px;
    padding: 16px;
    margin-top: 16px;
}

.chart-config-section {
    margin-bottom: 16px;
}

.chart-config-section:last-child {
    margin-bottom: 0;
}

.chart-config-label {
    font-size: 14px;
    font-weight: 600;
    color: #1c1c1e;
    margin-bottom: 8px;
    display: block;
}

.chart-config-input {
    width: 100%;
    background: #ffffff;
    border: 1px solid #d1d1d6;
    border-radius: 8px;
    padding: 12px;
    font-size: 16px;
    color: #1c1c1e;
    transition: border-color 0.2s ease;
}

.chart-config-input:focus {
    outline: none;
    border-color: #007aff;
}

/* 颜色选择器 */
.chart-color-palette {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.chart-color-option {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.chart-color-option.selected {
    border-color: #007aff;
    transform: scale(1.1);
}

.chart-color-option::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border-radius: 4px;
    background: inherit;
}

/* 图表加载状态 */
.chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #8e8e93;
}

.chart-loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f2f2f7;
    border-top: 3px solid #007aff;
    border-radius: 50%;
    animation: chart-spin 1s linear infinite;
    margin-bottom: 12px;
}

@keyframes chart-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.chart-loading-text {
    font-size: 14px;
}

/* 图表错误状态 */
.chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #ff3b30;
    text-align: center;
    padding: 20px;
}

.chart-error-icon {
    font-size: 48px;
    margin-bottom: 12px;
}

.chart-error-message {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}

.chart-error-detail {
    font-size: 14px;
    color: #8e8e93;
}

/* 图表工具提示 */
.chart-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chart-tooltip.visible {
    opacity: 1;
}

.chart-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

/* 图表图例 */
.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e5ea;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #1c1c1e;
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .chart-container {
        margin: 12px 0;
        padding: 12px;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .chart-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .chart-canvas-container {
        height: 250px;
    }
    
    .chart-type-selector {
        margin-bottom: 12px;
    }
    
    .chart-type-option {
        min-width: 70px;
        padding: 6px 8px;
    }
    
    .chart-config-panel {
        padding: 12px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .chart-container {
        background: #1c1c1e;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }
    
    .chart-title {
        color: #ffffff;
    }
    
    .chart-subtitle {
        color: #8e8e93;
    }
    
    .chart-action-button {
        background: #2c2c2e;
        color: #0a84ff;
    }
    
    .chart-action-button:hover {
        background: #3a3a3c;
    }
    
    .chart-type-selector {
        background: #2c2c2e;
    }
    
    .chart-type-option {
        color: #8e8e93;
    }
    
    .chart-type-option.active {
        background: #1c1c1e;
        color: #0a84ff;
    }
    
    .chart-config-panel {
        background: #2c2c2e;
    }
    
    .chart-config-label {
        color: #ffffff;
    }
    
    .chart-config-input {
        background: #1c1c1e;
        border-color: #3a3a3c;
        color: #ffffff;
    }
    
    .chart-config-input:focus {
        border-color: #0a84ff;
    }
    
    .chart-legend {
        border-top-color: #3a3a3c;
    }
    
    .chart-legend-item {
        color: #ffffff;
    }
}
