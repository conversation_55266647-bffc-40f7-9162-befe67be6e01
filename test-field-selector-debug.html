<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段选择器调试测试</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/ios-theme.css">
    <link rel="stylesheet" href="src/css/mobile.css">
    <link rel="stylesheet" href="src/css/components/field-selector.css">
    <link rel="stylesheet" href="src/css/components/toast.css">
    
    <style>
        body {
            padding: 20px;
            background-color: var(--background-primary);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .test-button {
            width: 100%;
            padding: 16px;
            background-color: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 16px;
        }
        
        .debug-info {
            background-color: var(--background-secondary);
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>字段选择器调试测试</h1>
        
        <div class="debug-info" id="debugInfo">
            等待初始化...
        </div>
        
        <button type="button" class="test-button" id="testButton">
            测试字段选择器
        </button>
        
        <button type="button" class="test-button" id="checkElementsButton">
            检查DOM元素
        </button>
        
        <button type="button" class="test-button" id="simulateClickButton">
            测试按钮功能
        </button>

        <button type="button" class="test-button" id="testValidationButton">
            测试数据验证修复
        </button>
    </div>
    
    <!-- 字段选择器容器 -->
    <div id="fieldSelectorContainer"></div>
    
    <!-- JavaScript文件 -->
    <script src="src/js/core/smartoffice-core.js"></script>
    <script src="src/js/core/smartoffice-events.js"></script>
    <script src="src/js/core/smartoffice-storage.js"></script>
    <script src="src/js/core/smartoffice-router.js"></script>
    
    <script src="src/js/utils/smartoffice-helpers.js"></script>
    <script src="src/js/utils/smartoffice-dom.js"></script>
    <script src="src/js/utils/smartoffice-format.js"></script>
    
    <script src="src/js/data/smartoffice-data-validator.js"></script>
    
    <script src="src/js/components/smartoffice-loading.js"></script>
    <script src="src/js/components/smartoffice-toast.js"></script>
    <script src="src/js/components/smartoffice-field-selector.js"></script>
    
    <script>
        // 调试信息显示
        function updateDebugInfo(message) {
            const debugInfo = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${timestamp}] ${message}<br>`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        // 测试数据 - 包含空字段名来测试验证修复
        const testFields = [
            { name: 'Order ID', type: 'string' },
            { name: '', type: 'string' },  // 空字段名
            { name: 'Product', type: 'string' },
            { name: '', type: 'number' },  // 另一个空字段名
            { name: 'Price', type: 'number' },
            { name: 'Order Date', type: 'date' },
            { name: 'Order Time', type: 'time' },
            { name: 'Status', type: 'string' }
        ];

        // 模拟包含空字段名的解析数据
        const testParsedData = {
            headers: ['Order ID', '', 'Product', '', 'Price', 'Order Date', 'Order Time', 'Status'],
            data: [
                { 'Order ID': '001', '': 'John', 'Product': 'Laptop', '': '2', 'Price': '999', 'Order Date': '2025-01-01', 'Order Time': '10:30', 'Status': 'Active' },
                { 'Order ID': '002', '': 'Jane', 'Product': 'Mouse', '': '1', 'Price': '25', 'Order Date': '2025-01-02', 'Order Time': '14:15', 'Status': 'Pending' }
            ]
        };
        
        let fieldSelector = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('页面加载完成');
            
            try {
                // 初始化字段选择器
                fieldSelector = new SmartOffice.Components.FieldSelector({
                    containerId: 'fieldSelectorContainer',
                    fields: testFields,
                    multiple: true,
                    title: '测试字段选择器',
                    onSelect: function(selectedFields, fieldType) {
                        updateDebugInfo(`选择完成: ${selectedFields.length}个字段`);
                        selectedFields.forEach(field => {
                            updateDebugInfo(`- ${field.name} (${field.type})`);
                        });
                    },
                    onCancel: function() {
                        updateDebugInfo('选择已取消');
                    }
                });
                
                const initSuccess = fieldSelector.init();
                updateDebugInfo(`字段选择器初始化: ${initSuccess ? '成功' : '失败'}`);
                
            } catch (error) {
                updateDebugInfo(`初始化错误: ${error.message}`);
                console.error('初始化错误:', error);
            }
        });
        
        // 测试按钮事件
        document.getElementById('testButton').addEventListener('click', function() {
            updateDebugInfo('点击测试按钮');
            
            if (fieldSelector) {
                try {
                    fieldSelector.show();
                    updateDebugInfo('字段选择器已显示');
                } catch (error) {
                    updateDebugInfo(`显示错误: ${error.message}`);
                    console.error('显示错误:', error);
                }
            } else {
                updateDebugInfo('字段选择器未初始化');
            }
        });
        
        // 检查DOM元素
        document.getElementById('checkElementsButton').addEventListener('click', function() {
            updateDebugInfo('检查DOM元素:');
            
            const container = document.getElementById('fieldSelectorContainer');
            updateDebugInfo(`容器存在: ${!!container}`);
            
            if (container) {
                updateDebugInfo(`容器内容: ${container.innerHTML.length > 0 ? '有内容' : '空'}`);
                
                const modal = container.querySelector('.so-field-selector-modal');
                updateDebugInfo(`模态框存在: ${!!modal}`);
                
                const backdrop = container.querySelector('.so-field-selector-backdrop');
                updateDebugInfo(`背景存在: ${!!backdrop}`);
                
                const fieldItems = container.querySelectorAll('.so-field-item');
                updateDebugInfo(`字段项数量: ${fieldItems.length}`);
                
                // 检查事件监听器
                if (fieldSelector && fieldSelector.listElement) {
                    updateDebugInfo('字段列表元素存在');
                } else {
                    updateDebugInfo('字段列表元素不存在');
                }
            }
        });
        
        // 模拟点击事件
        document.getElementById('simulateClickButton').addEventListener('click', function() {
            updateDebugInfo('测试按钮点击功能');

            const container = document.getElementById('fieldSelectorContainer');
            if (container) {
                const cancelButton = container.querySelector('#cancelButton');
                const confirmButton = container.querySelector('#confirmButton');

                updateDebugInfo(`取消按钮存在: ${!!cancelButton}`);
                updateDebugInfo(`确定按钮存在: ${!!confirmButton}`);

                if (cancelButton) {
                    updateDebugInfo('模拟点击取消按钮');
                    const clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    cancelButton.dispatchEvent(clickEvent);
                }

                setTimeout(() => {
                    if (confirmButton && fieldSelector && fieldSelector.isVisible) {
                        updateDebugInfo('模拟点击确定按钮');
                        const clickEvent = new MouseEvent('click', {
                            bubbles: true,
                            cancelable: true,
                            view: window
                        });
                        confirmButton.dispatchEvent(clickEvent);
                    }
                }, 1000);
            } else {
                updateDebugInfo('未找到字段选择器容器');
            }
        });
        
        // 监听全局错误
        window.addEventListener('error', function(event) {
            updateDebugInfo(`JavaScript错误: ${event.error.message}`);
            console.error('全局错误:', event.error);
        });
        
        // 测试数据验证修复
        document.getElementById('testValidationButton').addEventListener('click', function() {
            updateDebugInfo('开始测试数据验证修复');

            try {
                // 创建数据验证器
                const validator = new SmartOffice.Data.DataValidator();
                updateDebugInfo('数据验证器创建成功');

                // 测试验证包含空字段名的数据
                const validationResult = validator.validateParsedData(testParsedData);
                updateDebugInfo(`验证结果: ${validationResult.isValid ? '通过' : '失败'}`);
                updateDebugInfo(`验证消息: ${validationResult.message}`);

                if (validationResult.warnings && validationResult.warnings.length > 0) {
                    updateDebugInfo(`警告数量: ${validationResult.warnings.length}`);
                    validationResult.warnings.forEach((warning, index) => {
                        updateDebugInfo(`警告 ${index + 1}: ${warning}`);
                    });
                }

                if (validationResult.processedHeaders) {
                    updateDebugInfo('处理后的字段名:');
                    validationResult.processedHeaders.forEach((header, index) => {
                        updateDebugInfo(`  ${index + 1}. "${header}"`);
                    });
                }

                if (validationResult.isValid) {
                    updateDebugInfo('✅ 数据验证修复成功！空字段名已被正确处理');
                } else {
                    updateDebugInfo('❌ 数据验证仍然失败');
                }

            } catch (error) {
                updateDebugInfo(`测试出错: ${error.message}`);
                console.error('验证测试错误:', error);
            }
        });

        // 监听SmartOffice事件
        if (window.SmartOffice && SmartOffice.Core && SmartOffice.Core.EventBus) {
            SmartOffice.Core.EventBus.on('FIELD_SELECTOR_CONFIRM', function(data) {
                updateDebugInfo('收到字段选择确认事件');
            });

            SmartOffice.Core.EventBus.on('FIELD_SELECTOR_CANCEL', function(data) {
                updateDebugInfo('收到字段选择取消事件');
            });
        }
    </script>
</body>
</html>
