<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="GoMyHire透视分析">
    <title>GoMyHire 移动端快速透视分析</title>
    
    <!-- iOS风格CSS文件 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/ios-theme.css">
    <link rel="stylesheet" href="src/css/mobile.css">
    <link rel="stylesheet" href="src/css/components/config-list.css">
    <link rel="stylesheet" href="src/css/components/config-form.css">
    <link rel="stylesheet" href="src/css/components/config-form-integration.css">
    <link rel="stylesheet" href="src/css/components/file-upload.css">
    <link rel="stylesheet" href="src/css/components/dropdown.css">
    <link rel="stylesheet" href="src/css/components/field-selector.css">
    <link rel="stylesheet" href="src/css/components/data-table.css">
    <link rel="stylesheet" href="src/css/components/toast.css">
    
    <!-- iOS图标 -->
    <link rel="apple-touch-icon" sizes="180x180" href="src/assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="src/assets/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="src/assets/icons/favicon-16x16.png">
</head>
<body>
    <!-- iOS风格状态栏占位 -->
    <div class="status-bar-spacer"></div>
    
    <!-- 主应用容器 -->
    <div id="app" class="app-container">
        <!-- 导航栏 -->
        <header class="nav-bar">
            <div class="nav-content">
                <button class="nav-button nav-back nav-back-hidden" id="navBack" title="返回" aria-label="返回上一页">
                    <svg class="nav-icon" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                    </svg>
                </button>
                <h1 class="nav-title" id="navTitle">透视分析</h1>
                <button class="nav-button nav-add" id="navAdd" title="新建配置" aria-label="创建新的透视表配置">
                    <svg class="nav-icon" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                </button>
            </div>
        </header>
        
        <!-- 主内容区域 -->
        <main class="main-content" id="mainContent">
            <!-- 配置列表页面 -->
            <div class="page" id="configListPage">
                <div class="page-content">
                    <!-- 空状态提示 -->
                    <div class="empty-state" id="emptyState">
                        <div class="empty-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                            </svg>
                        </div>
                        <h3 class="empty-title">还没有透视表配置</h3>
                        <p class="empty-description">点击右上角的 + 按钮创建您的第一个透视表配置</p>
                    </div>
                    
                    <!-- 配置列表容器 -->
                    <div class="config-list" id="configList">
                        <!-- 配置卡片将在这里动态生成 -->
                    </div>
                </div>
            </div>
            
            <!-- 配置表单页面 -->
            <div class="page page-hidden" id="configFormPage">
                <div class="page-content">
                    <!-- 表单内容将由ConfigFormComponent动态生成 -->
                </div>
            </div>
        </main>
        
        <!-- iOS风格加载指示器 -->
        <div class="loading-overlay loading-overlay-hidden" id="loadingOverlay">
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
            </div>
        </div>
        
        <!-- iOS风格提示框容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <!-- JavaScript文件 - 按依赖顺序加载 -->

    <!-- 0. 第三方库 - Excel解压缩支持 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>

    <!-- 1. 核心基础设施 -->
    <script src="src/js/core/smartoffice-core.js"></script>
    <script src="src/js/core/smartoffice-events.js"></script>
    <script src="src/js/core/smartoffice-storage.js"></script>
    <script src="src/js/core/smartoffice-router.js"></script>
    <script src="src/js/core/smartoffice-offline.js"></script>
    
    <!-- 2. 工具函数 -->
    <script src="src/js/utils/smartoffice-helpers.js"></script>
    <script src="src/js/utils/smartoffice-dom.js"></script>
    <script src="src/js/utils/smartoffice-format.js"></script>
    
    <!-- 3. 数据处理模块 -->
    <script src="src/js/data/smartoffice-data-validator.js"></script>
    <script src="src/js/data/smartoffice-csv-parser.js"></script>
    <script src="src/js/data/smartoffice-config-manager.js"></script>  <!-- 修复路径 -->
    <script src="src/js/data/smartoffice-pivot-engine.js"></script>

    <!-- 3.1 文件解析器 -->
    <script src="src/js/parsers/smartoffice-excel-parser.js"></script>
    <script src="src/js/parsers/smartoffice-json-parser.js"></script>

    <!-- 3.2 模板管理 -->
    <script src="src/js/templates/smartoffice-template-manager.js"></script>

    <!-- 3.3 数据筛选 -->
    <script src="src/js/filters/smartoffice-data-filters.js"></script>

    <!-- 3.4 可视化模块 -->
    <script src="src/js/visualization/smartoffice-formatter.js"></script>
    <script src="src/js/visualization/smartoffice-chart-engine.js"></script>
    
    <!-- 4. UI组件 -->
    <script src="src/js/components/smartoffice-loading.js"></script>
    <script src="src/js/components/smartoffice-toast.js"></script>
    <script src="src/js/components/smartoffice-dropdown.js"></script>
    <script src="src/js/components/smartoffice-field-selector.js"></script>
    <script src="src/js/components/smartoffice-file-upload.js"></script>
    <script src="src/js/components/smartoffice-data-preview.js"></script>
    <script src="src/js/components/smartoffice-data-table.js"></script>
    <script src="src/js/components/smartoffice-config-form.js"></script>
    <script src="src/js/components/smartoffice-config-list.js"></script>
    
    <!-- 5. 应用主控制器 -->
    <script src="src/js/core/smartoffice-app.js"></script>
    
    <!-- 6. 应用启动 -->
    <script>
        /**
         * @function 应用初始化
         * 在DOM加载完成后启动SmartOffice应用
         */
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 检查关键文件是否加载成功
                const requiredComponents = [
                    'SmartOffice',
                    'SmartOffice.Core',
                    'SmartOffice.Core.App'
                ];
                
                for (const component of requiredComponents) {
                    const parts = component.split('.');
                    let obj = window;
                    for (const part of parts) {
                        if (!obj[part]) {
                            throw new Error(`必需组件 ${component} 未正确加载`);
                        }
                        obj = obj[part];
                    }
                }
                
                // 初始化应用
                SmartOffice.Core.App.init();
                
                // 开发模式下的调试信息
                if (SmartOffice.Config && SmartOffice.Config.DEBUG) {
                    console.log('🚀 GoMyHire移动端快速透视分析应用启动成功');
                    console.log('📱 iOS风格界面已加载');
                    console.log('🔧 传统JavaScript架构运行正常');
                }
            } catch (error) {
                console.error('❌ 应用启动失败:', error);
                // 显示用户友好的错误提示
                document.body.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #ff3b30; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        <h2 style="margin-bottom: 16px;">应用启动失败</h2>
                        <p style="margin-bottom: 12px;">请检查网络连接后刷新页面重试</p>
                        <p style="font-size: 12px; color: #8e8e93; margin-bottom: 20px;">${error.message}</p>
                        <button onclick="location.reload()" style="background: #007aff; color: white; border: none; padding: 8px 16px; border-radius: 8px; font-size: 16px;">
                            重新加载
                        </button>
                    </div>
                `;
            }
        });
        
        /**
         * @function 处理iOS Safari的特殊情况
         * 确保在iOS设备上的最佳体验
         */
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
            // 防止iOS Safari的橡皮筋效果
            document.addEventListener('touchmove', function(e) {
                if (e.target.closest('.scrollable')) {
                    return; // 允许可滚动区域的滚动
                }
                e.preventDefault();
            }, { passive: false });
            
            // 处理iOS Safari的视口高度问题
            function updateViewportHeight() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', vh + 'px');
            }
            
            updateViewportHeight();
            window.addEventListener('resize', updateViewportHeight);
            window.addEventListener('orientationchange', function() {
                setTimeout(updateViewportHeight, 100);
            });
        }
    </script>
</body>
</html>
