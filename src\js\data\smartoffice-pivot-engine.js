/**
 * @file SmartOffice透视表计算引擎
 * @description 纯JavaScript实现的透视表计算引擎，支持多维数据分组和聚合
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function PivotEngine
 * @description 透视表计算引擎构造函数
 * @constructor
 * @param {Object} options - 配置选项
 * @param {number} options.maxRows - 最大处理行数（默认100000）
 * @param {number} options.maxColumns - 最大列数（默认1000）
 * @param {boolean} options.enableCache - 是否启用缓存（默认true）
 */
function PivotEngine(options) {
    // 默认配置
    this.options = SmartOffice.Utils.Helpers.extend({
        maxRows: 100000,
        maxColumns: 1000,
        enableCache: true,
        precision: 2  // 数值精度
    }, options || {});

    // 引擎状态
    this.isProcessing = false;
    this.currentProgress = 0;
    this.cache = new Map();

    // 聚合函数映射
    this.aggregationFunctions = {
        'sum': this.calculateSum,
        'count': this.calculateCount,
        'average': this.calculateAverage,
        'min': this.calculateMin,
        'max': this.calculateMax,
        'countDistinct': this.calculateCountDistinct
    };

    // 依赖注入
    this.helpers = SmartOffice.Utils.Helpers;
    this.dataValidator = new SmartOffice.Data.DataValidator();
    this.eventBus = SmartOffice.Core.EventBus;

    SmartOffice.log('info', 'PivotEngine初始化完成');
}

/**
 * @function PivotEngine.prototype.calculate
 * @description 计算透视表
 * @param {Array} data - 源数据数组
 * @param {Object} config - 透视表配置
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise} 计算结果Promise
 */
PivotEngine.prototype.calculate = function(data, config, onProgress) {
    const self = this;

    return new Promise(function(resolve, reject) {
        try {
            // 验证输入参数
            const validation = self.validateInput(data, config);
            if (!validation.isValid) {
                reject(new Error(validation.message));
                return;
            }

            // 设置处理状态
            self.isProcessing = true;
            self.currentProgress = 0;

            // 异步处理，避免阻塞UI
            setTimeout(function() {
                try {
                    const result = self.performCalculation(data, config, onProgress);
                    self.isProcessing = false;
                    resolve(result);
                } catch (error) {
                    self.isProcessing = false;
                    reject(error);
                }
            }, 10);

        } catch (error) {
            self.isProcessing = false;
            reject(error);
        }
    });
};

/**
 * @function PivotEngine.prototype.validateInput
 * @description 验证输入参数
 * @param {Array} data - 源数据
 * @param {Object} config - 配置对象
 * @returns {Object} 验证结果
 */
PivotEngine.prototype.validateInput = function(data, config) {
    // 验证数据
    if (!Array.isArray(data) || data.length === 0) {
        return { isValid: false, message: '数据为空或格式无效' };
    }

    if (data.length > this.options.maxRows) {
        return { isValid: false, message: `数据行数超过限制（最大${this.options.maxRows}行）` };
    }

    // 验证配置
    const configValidation = this.dataValidator.validatePivotConfig(config);
    if (!configValidation.isValid) {
        return configValidation;
    }

    // 验证字段存在性
    const firstRow = data[0];
    const allFields = [
        ...config.rowFields,
        ...config.columnFields,
        ...config.valueFields,
        ...config.filterFields
    ];

    for (let i = 0; i < allFields.length; i++) {
        const field = allFields[i];
        if (!firstRow.hasOwnProperty(field)) {
            return { isValid: false, message: `字段 "${field}" 在数据中不存在` };
        }
    }

    return { isValid: true, message: '验证通过' };
};

/**
 * @function PivotEngine.prototype.performCalculation
 * @description 执行透视表计算
 * @param {Array} data - 源数据
 * @param {Object} config - 配置对象
 * @param {Function} onProgress - 进度回调
 * @returns {Object} 计算结果
 */
PivotEngine.prototype.performCalculation = function(data, config, onProgress) {
    const startTime = Date.now();

    // 1. 应用筛选条件
    this.updateProgress(10, '应用筛选条件...', onProgress);
    const filteredData = this.applyFilters(data, config.filterFields);

    // 2. 数据分组
    this.updateProgress(30, '数据分组中...', onProgress);
    const groupedData = this.groupData(filteredData, config.rowFields, config.columnFields);

    // 3. 计算聚合值
    this.updateProgress(60, '计算聚合值...', onProgress);
    const aggregatedData = this.aggregateData(groupedData, config.valueFields, config.aggregationType);

    // 4. 构建透视表结构
    this.updateProgress(80, '构建透视表...', onProgress);
    const pivotTable = this.buildPivotTable(aggregatedData, config);

    // 5. 生成最终结果
    this.updateProgress(100, '计算完成', onProgress);

    const result = {
        table: pivotTable,
        summary: this.generateSummary(pivotTable, filteredData.length),
        config: config,
        metadata: {
            originalRows: data.length,
            filteredRows: filteredData.length,
            calculationTime: Date.now() - startTime,
            timestamp: new Date().toISOString()
        }
    };

    SmartOffice.log('info', '透视表计算完成，耗时:', result.metadata.calculationTime + 'ms');
    return result;
};

/**
 * @function PivotEngine.prototype.applyFilters
 * @description 应用筛选条件
 * @param {Array} data - 源数据
 * @param {Array} filterFields - 筛选字段配置
 * @returns {Array} 筛选后的数据
 */
PivotEngine.prototype.applyFilters = function(data, filterFields) {
    if (!filterFields || filterFields.length === 0) {
        return data;
    }

    // 目前返回原数据，后续可扩展筛选逻辑
    return data;
};

/**
 * @function PivotEngine.prototype.groupData
 * @description 数据分组
 * @param {Array} data - 源数据
 * @param {Array} rowFields - 行字段
 * @param {Array} columnFields - 列字段
 * @returns {Map} 分组后的数据
 */
PivotEngine.prototype.groupData = function(data, rowFields, columnFields) {
    const groups = new Map();

    for (let i = 0; i < data.length; i++) {
        const row = data[i];

        // 生成行键
        const rowKey = this.generateGroupKey(row, rowFields);

        // 生成列键
        const columnKey = this.generateGroupKey(row, columnFields);

        // 组合键
        const groupKey = rowKey + '|' + columnKey;

        if (!groups.has(groupKey)) {
            groups.set(groupKey, {
                rowKey: rowKey,
                columnKey: columnKey,
                rowValues: this.extractFieldValues(row, rowFields),
                columnValues: this.extractFieldValues(row, columnFields),
                data: []
            });
        }

        groups.get(groupKey).data.push(row);
    }

    return groups;
};

/**
 * @function PivotEngine.prototype.generateGroupKey
 * @description 生成分组键
 * @param {Object} row - 数据行
 * @param {Array} fields - 字段数组
 * @returns {string} 分组键
 */
PivotEngine.prototype.generateGroupKey = function(row, fields) {
    if (!fields || fields.length === 0) {
        return '__total__';
    }

    const values = [];
    for (let i = 0; i < fields.length; i++) {
        const field = fields[i];
        const value = row[field];
        values.push(value === null || value === undefined ? '__null__' : String(value));
    }

    return values.join('::');
};

/**
 * @function PivotEngine.prototype.extractFieldValues
 * @description 提取字段值
 * @param {Object} row - 数据行
 * @param {Array} fields - 字段数组
 * @returns {Array} 字段值数组
 */
PivotEngine.prototype.extractFieldValues = function(row, fields) {
    const values = [];
    for (let i = 0; i < fields.length; i++) {
        const field = fields[i];
        values.push(row[field]);
    }
    return values;
};

/**
 * @function PivotEngine.prototype.aggregateData
 * @description 聚合数据
 * @param {Map} groupedData - 分组数据
 * @param {Array} valueFields - 值字段
 * @param {string} aggregationType - 聚合类型
 * @returns {Map} 聚合后的数据
 */
PivotEngine.prototype.aggregateData = function(groupedData, valueFields, aggregationType) {
    const aggregatedData = new Map();

    for (const [groupKey, group] of groupedData) {
        const aggregatedValues = {};

        for (let i = 0; i < valueFields.length; i++) {
            const field = valueFields[i];
            const values = group.data.map(row => row[field]);

            // 执行聚合计算
            aggregatedValues[field] = this.performAggregation(values, aggregationType);
        }

        aggregatedData.set(groupKey, {
            rowKey: group.rowKey,
            columnKey: group.columnKey,
            rowValues: group.rowValues,
            columnValues: group.columnValues,
            values: aggregatedValues,
            count: group.data.length
        });
    }

    return aggregatedData;
};

/**
 * @function PivotEngine.prototype.performAggregation
 * @description 执行聚合计算
 * @param {Array} values - 值数组
 * @param {string} aggregationType - 聚合类型
 * @returns {number} 聚合结果
 */
PivotEngine.prototype.performAggregation = function(values, aggregationType) {
    const aggregationFunction = this.aggregationFunctions[aggregationType];
    if (!aggregationFunction) {
        throw new Error('不支持的聚合类型: ' + aggregationType);
    }

    return aggregationFunction.call(this, values);
};

/**
 * @function PivotEngine.prototype.calculateSum
 * @description 计算求和
 * @param {Array} values - 值数组
 * @returns {number} 求和结果
 */
PivotEngine.prototype.calculateSum = function(values) {
    let sum = 0;
    for (let i = 0; i < values.length; i++) {
        const value = parseFloat(values[i]);
        if (!isNaN(value)) {
            sum += value;
        }
    }
    return this.roundNumber(sum);
};

/**
 * @function PivotEngine.prototype.calculateCount
 * @description 计算计数
 * @param {Array} values - 值数组
 * @returns {number} 计数结果
 */
PivotEngine.prototype.calculateCount = function(values) {
    let count = 0;
    for (let i = 0; i < values.length; i++) {
        if (values[i] !== null && values[i] !== undefined && values[i] !== '') {
            count++;
        }
    }
    return count;
};

/**
 * @function PivotEngine.prototype.calculateAverage
 * @description 计算平均值
 * @param {Array} values - 值数组
 * @returns {number} 平均值结果
 */
PivotEngine.prototype.calculateAverage = function(values) {
    const sum = this.calculateSum(values);
    const count = this.calculateCount(values);
    return count > 0 ? this.roundNumber(sum / count) : 0;
};

/**
 * @function PivotEngine.prototype.calculateMin
 * @description 计算最小值
 * @param {Array} values - 值数组
 * @returns {number} 最小值结果
 */
PivotEngine.prototype.calculateMin = function(values) {
    let min = Infinity;
    for (let i = 0; i < values.length; i++) {
        const value = parseFloat(values[i]);
        if (!isNaN(value) && value < min) {
            min = value;
        }
    }
    return min === Infinity ? 0 : this.roundNumber(min);
};

/**
 * @function PivotEngine.prototype.calculateMax
 * @description 计算最大值
 * @param {Array} values - 值数组
 * @returns {number} 最大值结果
 */
PivotEngine.prototype.calculateMax = function(values) {
    let max = -Infinity;
    for (let i = 0; i < values.length; i++) {
        const value = parseFloat(values[i]);
        if (!isNaN(value) && value > max) {
            max = value;
        }
    }
    return max === -Infinity ? 0 : this.roundNumber(max);
};

/**
 * @function PivotEngine.prototype.calculateCountDistinct
 * @description 计算去重计数
 * @param {Array} values - 值数组
 * @returns {number} 去重计数结果
 */
PivotEngine.prototype.calculateCountDistinct = function(values) {
    const uniqueValues = new Set();
    for (let i = 0; i < values.length; i++) {
        if (values[i] !== null && values[i] !== undefined && values[i] !== '') {
            uniqueValues.add(values[i]);
        }
    }
    return uniqueValues.size;
};

/**
 * @function PivotEngine.prototype.buildPivotTable
 * @description 构建透视表结构
 * @param {Map} aggregatedData - 聚合数据
 * @param {Object} config - 配置对象
 * @returns {Object} 透视表结构
 */
PivotEngine.prototype.buildPivotTable = function(aggregatedData, config) {
    // 收集所有行键和列键
    const rowKeys = new Set();
    const columnKeys = new Set();

    for (const [groupKey, group] of aggregatedData) {
        rowKeys.add(group.rowKey);
        columnKeys.add(group.columnKey);
    }

    // 转换为排序数组
    const sortedRowKeys = Array.from(rowKeys).sort();
    const sortedColumnKeys = Array.from(columnKeys).sort();

    // 构建表头
    const headers = this.buildTableHeaders(sortedColumnKeys, config.columnFields, config.valueFields);

    // 构建数据行
    const rows = this.buildTableRows(sortedRowKeys, sortedColumnKeys, aggregatedData, config);

    return {
        headers: headers,
        rows: rows,
        rowKeys: sortedRowKeys,
        columnKeys: sortedColumnKeys,
        totalRows: rows.length,
        totalColumns: headers.length
    };
};

/**
 * @function PivotEngine.prototype.buildTableHeaders
 * @description 构建表头
 * @param {Array} columnKeys - 列键数组
 * @param {Array} columnFields - 列字段
 * @param {Array} valueFields - 值字段
 * @returns {Array} 表头数组
 */
PivotEngine.prototype.buildTableHeaders = function(columnKeys, columnFields, valueFields) {
    const headers = [];

    // 添加行字段标题
    headers.push('维度');

    // 添加列字段标题
    for (let i = 0; i < columnKeys.length; i++) {
        const columnKey = columnKeys[i];
        const displayName = columnKey === '__total__' ? '总计' : columnKey.replace(/::/g, ' - ');

        if (valueFields.length === 1) {
            headers.push(displayName);
        } else {
            // 多个值字段时，为每个值字段创建列
            for (let j = 0; j < valueFields.length; j++) {
                headers.push(`${displayName} (${valueFields[j]})`);
            }
        }
    }

    return headers;
};

/**
 * @function PivotEngine.prototype.buildTableRows
 * @description 构建数据行
 * @param {Array} rowKeys - 行键数组
 * @param {Array} columnKeys - 列键数组
 * @param {Map} aggregatedData - 聚合数据
 * @param {Object} config - 配置对象
 * @returns {Array} 数据行数组
 */
PivotEngine.prototype.buildTableRows = function(rowKeys, columnKeys, aggregatedData, config) {
    const rows = [];

    for (let i = 0; i < rowKeys.length; i++) {
        const rowKey = rowKeys[i];
        const row = [];

        // 添加行标签
        const rowLabel = rowKey === '__total__' ? '总计' : rowKey.replace(/::/g, ' - ');
        row.push(rowLabel);

        // 添加数据值
        for (let j = 0; j < columnKeys.length; j++) {
            const columnKey = columnKeys[j];
            const groupKey = rowKey + '|' + columnKey;
            const group = aggregatedData.get(groupKey);

            if (group) {
                if (config.valueFields.length === 1) {
                    const field = config.valueFields[0];
                    row.push(group.values[field] || 0);
                } else {
                    // 多个值字段
                    for (let k = 0; k < config.valueFields.length; k++) {
                        const field = config.valueFields[k];
                        row.push(group.values[field] || 0);
                    }
                }
            } else {
                // 没有数据的单元格
                const emptyCells = config.valueFields.length;
                for (let k = 0; k < emptyCells; k++) {
                    row.push(0);
                }
            }
        }

        rows.push(row);
    }

    return rows;
};

/**
 * @function PivotEngine.prototype.generateSummary
 * @description 生成汇总信息
 * @param {Object} pivotTable - 透视表
 * @param {number} dataCount - 数据行数
 * @returns {Object} 汇总信息
 */
PivotEngine.prototype.generateSummary = function(pivotTable, dataCount) {
    return {
        totalDataRows: dataCount,
        pivotRows: pivotTable.totalRows,
        pivotColumns: pivotTable.totalColumns,
        uniqueRowGroups: pivotTable.rowKeys.length,
        uniqueColumnGroups: pivotTable.columnKeys.length
    };
};

/**
 * @function PivotEngine.prototype.updateProgress
 * @description 更新进度
 * @param {number} percentage - 进度百分比
 * @param {string} message - 进度消息
 * @param {Function} onProgress - 进度回调
 */
PivotEngine.prototype.updateProgress = function(percentage, message, onProgress) {
    this.currentProgress = percentage;

    if (onProgress && typeof onProgress === 'function') {
        onProgress(percentage, message);
    }

    this.eventBus.emit(SmartOffice.Events.PIVOT_CALCULATION_PROGRESS, {
        percentage: percentage,
        message: message
    });
};

/**
 * @function PivotEngine.prototype.roundNumber
 * @description 数值四舍五入
 * @param {number} number - 数值
 * @returns {number} 四舍五入后的数值
 */
PivotEngine.prototype.roundNumber = function(number) {
    const factor = Math.pow(10, this.options.precision);
    return Math.round(number * factor) / factor;
};

/**
 * @function PivotEngine.prototype.getProgress
 * @description 获取当前进度
 * @returns {number} 当前进度百分比
 */
PivotEngine.prototype.getProgress = function() {
    return this.currentProgress;
};

/**
 * @function PivotEngine.prototype.isCurrentlyProcessing
 * @description 检查是否正在处理
 * @returns {boolean} 是否正在处理
 */
PivotEngine.prototype.isCurrentlyProcessing = function() {
    return this.isProcessing;
};

/**
 * @function PivotEngine.prototype.clearCache
 * @description 清空缓存
 */
PivotEngine.prototype.clearCache = function() {
    this.cache.clear();
    SmartOffice.log('info', '透视表引擎缓存已清空');
};

// 注册到全局命名空间
SmartOffice.Data.PivotEngine = PivotEngine;

SmartOffice.log('info', 'SmartOffice透视表引擎模块初始化完成');
