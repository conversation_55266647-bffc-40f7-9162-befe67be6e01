/**
 * @file SmartOffice存储管理模块
 * @description 本地存储管理，支持localStorage和数据序列化
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function StorageManager
 * @description 存储管理器构造函数
 * @constructor
 */
function StorageManager() {
    /**
     * @property {string} prefix - 存储键前缀
     */
    this.prefix = SmartOffice.Config.STORAGE_PREFIX;
    
    /**
     * @property {boolean} available - localStorage是否可用
     */
    this.available = this.checkAvailability();
    
    /**
     * @property {Object} cache - 内存缓存
     */
    this.cache = {};
    
    SmartOffice.log('info', 'StorageManager初始化完成', {
        available: this.available,
        prefix: this.prefix
    });
}

/**
 * @function StorageManager.prototype.checkAvailability
 * @description 检查localStorage是否可用
 * @returns {boolean} 是否可用
 */
StorageManager.prototype.checkAvailability = function() {
    try {
        const testKey = this.prefix + 'test';
        localStorage.setItem(testKey, 'test');
        localStorage.removeItem(testKey);
        return true;
    } catch (error) {
        SmartOffice.log('warn', 'localStorage不可用，将使用内存缓存:', error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.getKey
 * @description 获取带前缀的完整键名
 * @param {string} key - 原始键名
 * @returns {string} 完整键名
 */
StorageManager.prototype.getKey = function(key) {
    return this.prefix + key;
};

/**
 * @function StorageManager.prototype.set
 * @description 设置存储值
 * @param {string} key - 键名
 * @param {*} value - 值
 * @returns {boolean} 是否设置成功
 */
StorageManager.prototype.set = function(key, value) {
    if (typeof key !== 'string') {
        SmartOffice.log('error', '存储键名必须是字符串:', key);
        return false;
    }
    
    try {
        const fullKey = this.getKey(key);
        const serializedValue = JSON.stringify({
            data: value,
            timestamp: Date.now(),
            version: SmartOffice.Config.STORAGE_VERSION
        });
        
        if (this.available) {
            localStorage.setItem(fullKey, serializedValue);
        }
        
        // 同时更新内存缓存
        this.cache[key] = value;
        
        SmartOffice.log('debug', '存储值已设置:', key);
        return true;
        
    } catch (error) {
        SmartOffice.log('error', '设置存储值失败:', {
            key: key,
            error: error.message
        });
        return false;
    }
};

/**
 * @function StorageManager.prototype.get
 * @description 获取存储值
 * @param {string} key - 键名
 * @param {*} defaultValue - 默认值
 * @returns {*} 存储的值或默认值
 */
StorageManager.prototype.get = function(key, defaultValue) {
    if (typeof key !== 'string') {
        SmartOffice.log('error', '存储键名必须是字符串:', key);
        return defaultValue;
    }
    
    try {
        // 先检查内存缓存
        if (this.cache.hasOwnProperty(key)) {
            return this.cache[key];
        }
        
        // 从localStorage读取
        if (this.available) {
            const fullKey = this.getKey(key);
            const serializedValue = localStorage.getItem(fullKey);
            
            if (serializedValue !== null) {
                const parsed = JSON.parse(serializedValue);
                
                // 检查版本兼容性
                if (parsed.version && parsed.version !== SmartOffice.Config.STORAGE_VERSION) {
                    SmartOffice.log('warn', '存储版本不匹配，使用默认值:', {
                        key: key,
                        storedVersion: parsed.version,
                        currentVersion: SmartOffice.Config.STORAGE_VERSION
                    });
                    return defaultValue;
                }
                
                // 更新内存缓存
                this.cache[key] = parsed.data;
                
                SmartOffice.log('debug', '从存储获取值:', key);
                return parsed.data;
            }
        }
        
        SmartOffice.log('debug', '存储中未找到值，返回默认值:', key);
        return defaultValue;
        
    } catch (error) {
        SmartOffice.log('error', '获取存储值失败:', {
            key: key,
            error: error.message
        });
        return defaultValue;
    }
};

/**
 * @function StorageManager.prototype.remove
 * @description 移除存储值
 * @param {string} key - 键名
 * @returns {boolean} 是否移除成功
 */
StorageManager.prototype.remove = function(key) {
    if (typeof key !== 'string') {
        SmartOffice.log('error', '存储键名必须是字符串:', key);
        return false;
    }
    
    try {
        const fullKey = this.getKey(key);
        
        if (this.available) {
            localStorage.removeItem(fullKey);
        }
        
        // 从内存缓存中移除
        delete this.cache[key];
        
        SmartOffice.log('debug', '存储值已移除:', key);
        return true;
        
    } catch (error) {
        SmartOffice.log('error', '移除存储值失败:', {
            key: key,
            error: error.message
        });
        return false;
    }
};

/**
 * @function StorageManager.prototype.clear
 * @description 清空所有存储值
 * @returns {boolean} 是否清空成功
 */
StorageManager.prototype.clear = function() {
    try {
        if (this.available) {
            // 只清除带有指定前缀的项
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.prefix)) {
                    keysToRemove.push(key);
                }
            }
            
            for (let i = 0; i < keysToRemove.length; i++) {
                localStorage.removeItem(keysToRemove[i]);
            }
        }
        
        // 清空内存缓存
        this.cache = {};
        
        SmartOffice.log('info', '所有存储值已清空');
        return true;
        
    } catch (error) {
        SmartOffice.log('error', '清空存储失败:', error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.has
 * @description 检查是否存在指定键
 * @param {string} key - 键名
 * @returns {boolean} 是否存在
 */
StorageManager.prototype.has = function(key) {
    if (typeof key !== 'string') {
        return false;
    }
    
    // 检查内存缓存
    if (this.cache.hasOwnProperty(key)) {
        return true;
    }
    
    // 检查localStorage
    if (this.available) {
        const fullKey = this.getKey(key);
        return localStorage.getItem(fullKey) !== null;
    }
    
    return false;
};

/**
 * @function StorageManager.prototype.keys
 * @description 获取所有存储键名
 * @returns {Array} 键名列表
 */
StorageManager.prototype.keys = function() {
    const keys = [];
    
    // 从内存缓存获取
    for (const key in this.cache) {
        if (this.cache.hasOwnProperty(key)) {
            keys.push(key);
        }
    }
    
    // 从localStorage获取
    if (this.available) {
        for (let i = 0; i < localStorage.length; i++) {
            const fullKey = localStorage.key(i);
            if (fullKey && fullKey.startsWith(this.prefix)) {
                const key = fullKey.substring(this.prefix.length);
                if (keys.indexOf(key) === -1) {
                    keys.push(key);
                }
            }
        }
    }
    
    return keys;
};

/**
 * @function StorageManager.prototype.size
 * @description 获取存储项数量
 * @returns {number} 存储项数量
 */
StorageManager.prototype.size = function() {
    return this.keys().length;
};

/**
 * @function StorageManager.prototype.getUsage
 * @description 获取localStorage使用情况
 * @returns {Object} 使用情况信息
 */
StorageManager.prototype.getUsage = function() {
    if (!this.available) {
        return {
            used: 0,
            total: 0,
            available: 0,
            percentage: 0
        };
    }
    
    try {
        let used = 0;
        
        // 计算当前应用使用的存储空间
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.prefix)) {
                const value = localStorage.getItem(key);
                used += key.length + (value ? value.length : 0);
            }
        }
        
        // 尝试估算总可用空间
        const testData = new Array(1024).join('a'); // 1KB的数据
        let total = 0;
        
        try {
            let testKey = this.prefix + 'size_test_';
            while (true) {
                localStorage.setItem(testKey + total, testData);
                total += testData.length;
                localStorage.removeItem(testKey + total);
            }
        } catch (e) {
            // 达到存储限制
        }
        
        return {
            used: used,
            total: Math.max(total, used),
            available: Math.max(0, total - used),
            percentage: total > 0 ? (used / total * 100) : 0
        };
        
    } catch (error) {
        SmartOffice.log('error', '获取存储使用情况失败:', error);
        return {
            used: 0,
            total: 0,
            available: 0,
            percentage: 0
        };
    }
};

/**
 * @function StorageManager.prototype.export
 * @description 导出所有存储数据
 * @returns {Object} 导出的数据
 */
StorageManager.prototype.export = function() {
    const data = {};
    const keys = this.keys();
    
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        data[key] = this.get(key);
    }
    
    return {
        version: SmartOffice.Config.STORAGE_VERSION,
        timestamp: Date.now(),
        data: data
    };
};

/**
 * @function StorageManager.prototype.import
 * @description 导入存储数据
 * @param {Object} exportedData - 导出的数据
 * @returns {boolean} 是否导入成功
 */
StorageManager.prototype.import = function(exportedData) {
    try {
        if (!exportedData || !exportedData.data) {
            SmartOffice.log('error', '导入数据格式无效');
            return false;
        }
        
        // 检查版本兼容性
        if (exportedData.version !== SmartOffice.Config.STORAGE_VERSION) {
            SmartOffice.log('warn', '导入数据版本不匹配');
        }
        
        // 导入数据
        const data = exportedData.data;
        for (const key in data) {
            if (data.hasOwnProperty(key)) {
                this.set(key, data[key]);
            }
        }
        
        SmartOffice.log('info', '存储数据导入成功');
        return true;
        
    } catch (error) {
        SmartOffice.log('error', '导入存储数据失败:', error);
        return false;
    }
};

// 创建全局存储管理器实例
SmartOffice.Core.Storage = new StorageManager();

SmartOffice.log('info', 'SmartOffice存储管理模块初始化完成');
